import React, { useMemo } from "react";

import MarkdownRender from "./Markdown";

interface TextBlockProps {
  content: string;
}

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;
  const trimmedContent = content?.trim();

  const contentMemo = useMemo(() => {
    return (
      <div className="cscs-agent-text-block ag:my-4 ag:first:mt-0 ag:last:mb-0 ag:break-all ag:leading-[1.6em]">
        <MarkdownRender content={trimmedContent} />
      </div>
    );
  }, [trimmedContent]);

  if (trimmedContent === "") return null;

  return contentMemo;
};

export default TextBlock;
