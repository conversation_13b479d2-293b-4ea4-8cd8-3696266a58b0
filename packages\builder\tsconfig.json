{
  "compilerOptions": {
    "target": "ES2022",
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "outDir": "./lib",
    "rootDir": "./src",
    "resolveJsonModule": true,
    "types": [
      "node"
    ],
    "sourceMap": true
  },
  "include": [
    "src/**/*"
, "bin", "tpl/vite.config.js"  ],
  "exclude": [
    "node_modules",
    "lib",
  ]
}