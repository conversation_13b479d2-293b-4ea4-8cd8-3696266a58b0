import { CommonServerOptions } from "vite";

const config: CommonServerOptions["proxy"] = {
  "^/api": {
    // target: "http://************:5800",
    target: "http://localhost:8000",
    changeOrigin: true,
  },
  "^/agent-api": {
    target: "http://************:5800",
    // target: "http://localhost:8000",
    changeOrigin: true,
    rewrite(path) {
      return path.replace(/^\/agent-api/, "/api");
    },
  },
};

export default config;
