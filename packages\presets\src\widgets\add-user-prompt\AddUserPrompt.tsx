import { Button, Form, Input, Modal, Tooltip, message } from "antd";
import React, { useState } from "react";

import { PresetsCommand } from "@/command";
import { MessageContext, StandardResponse, post, useActiveAgentCode, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const AddUserPrompt: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const context = React.useContext(MessageContext);
  const text = context?.message.getTextContent() ?? "";
  const [activeAgentCode] = useActiveAgentCode();
  const runner = useCommandRunner();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const _values = {
        ...values,
        agent_code: activeAgentCode,
      };
      post<StandardResponse>("/user-prompt-template", _values).then((res) => {
        if (res.data.code === 200) {
          message.success("新建成功");
          setOpen(false);
          runner(PresetsCommand.ReloadUserPrompt);
        }
      });
    } catch {}
  };

  const openModal = () => {
    setOpen(true);
    form.setFieldsValue({
      prompt: text,
    });
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <>
      <Tooltip title="存为自定义模板">
        <Button
          type="text"
          size="small"
          icon={
            <span className="pts:text-black-65">
              <Icon icon="AddPrompt" />
            </span>
          }
          onClick={openModal}
        />
      </Tooltip>
      <Modal open={open} onCancel={handleCancel} onOk={handleOk} title={"新建自定义模板"}>
        <div className="pts:pt-4">
          <Form form={form} labelCol={{ span: 4 }}>
            <Form.Item
              label="模板标题"
              name="title"
              rules={[
                {
                  required: true,
                  message: "请输入模板标题",
                },
                {
                  max: 15,
                  message: "最多15个字",
                },
              ]}
            >
              <Input placeholder="请输入最多15个字" />
            </Form.Item>
            <Form.Item
              label="模板描述"
              name="description"
              rules={[
                {
                  max: 20,
                  message: "最多20个字",
                },
              ]}
            >
              <Input placeholder="请输入最多20个字" />
            </Form.Item>
            <Form.Item
              label="模板内容"
              name="prompt"
              rules={[
                {
                  required: true,
                  message: "请输入模板内容",
                },
                {
                  max: 1000,
                  message: "最多1000个字",
                },
              ]}
            >
              <Input.TextArea placeholder="请输入提示词内容，最多1000字，如帮我写xx企业预警报告" />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default AddUserPrompt;
