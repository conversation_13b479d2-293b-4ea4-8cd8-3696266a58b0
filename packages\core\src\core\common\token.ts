// 从localStorage获取Token
function getToken() {
  const tokenStr = localStorage.getItem("token");

  if (tokenStr) {
    try {
      // 尝试解析JSON字符串
      return JSON.parse(tokenStr).access_token;
    } catch (e) {
      console.error("Failed to parse token:", e);
    }
  }
  return null;
}

function setToken(token: string) {
  localStorage.setItem("token", JSON.stringify({ access_token: token }));
}

function clearToken() {
  localStorage.removeItem("token");
}

export interface TokenHandlesSettingOption {
  getToken?: () => string;
  setToken?: (token: any) => void;
  clearToken?: () => void;
}

export let tokenHandles = {
  getToken,
  setToken,
  clearToken,
};

export function setTokenHandles(option: TokenHandlesSettingOption) {
  tokenHandles = {
    ...tokenHandles,
    ...option,
  };
}
