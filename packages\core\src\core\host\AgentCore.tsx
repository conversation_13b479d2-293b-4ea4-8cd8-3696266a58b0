import React, { useEffect, useState } from "react";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import { MessageReceiver, MessageReceiverEvent } from "@/message";
import { post } from "@/request";
import {
  BuildInCommand,
  HeaderMessagePackage,
  IMessagePackage,
  ISendMessageParams,
  MessageStatus,
  Role,
} from "@/types";

import { createMessage } from "../common/message";
import { useAgentConfigs } from "../hooks/useAgent";
import {
  useActiveAgentCode,
  useActiveConversationId,
  useConversationState,
  useHumanMessage,
  useIsLoadingMessage,
  useMessages,
} from "../state/hooks";
import { Transmitter } from "../transmitter/transmitter";
import { updateLoadingMessagePackage } from "./utils";

const AgentCore: React.FC<{ agentCode?: string }> = (props) => {
  const { agentCode, children } = props;
  const [transmitter, setTransmitter] = useState<Transmitter | null>(null);
  const agentConfigs = useAgentConfigs();
  const { create: createHumanMessage } = useHumanMessage();
  const [, setMessages] = useMessages();
  const [isLoadingMessage, setIsLoadingMessage] = useIsLoadingMessage();
  const runner = useCommandRunner();
  const isLoadingMessageRef = React.useRef(false);
  const [activeConversationId] = useActiveConversationId();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const { setAllState } = useConversationState();

  useEffect(() => {
    if (agentCode) {
      setActiveAgentCode(agentCode);
    }
  }, [agentCode]);

  // 取消上一次的请求
  const cancelStaleChatRequest = ($transmitter?: Transmitter | null) => {
    if ($transmitter) {
      $transmitter.cancel();
    }
    setTransmitter(null);
    setIsLoadingMessage(false);
    setMessages((messages) => {
      const message = messages[messages.length - 1];
      if (!message) {
        return messages;
      }
      if (isLoadingMessage) {
        message.status = MessageStatus.Cancelled;
      }
      return [...messages];
    });
  };

  const sendMessage = (params: ISendMessageParams, ignoreHumanMessage: boolean = false) => {
    const {
      message,
      agentCode,
      conversationId,
      isNewConversation,
      extendParams,
      extendData,
      conversationPreState,
      hidden,
      files,
    } = params;
    if (isLoadingMessage) {
      cancelStaleChatRequest();
    }
    if (!ignoreHumanMessage) {
      createHumanMessage(message, extendData, hidden);
    }

    const config = agentConfigs.find((i) => i.code === agentCode);
    const url = config?.request.chat.url;
    const options = {
      headers: config?.request.chat.headers,
      method: config?.request.chat.method,
      // body: config?.request.chat.body,
    };
    const $transmitter = new Transmitter(url, options);

    isLoadingMessageRef.current = true;

    runner(BuildInCommand.EmitMessageSendedEvent);
    // 发送消息
    $transmitter
      .send({ message, agentCode, conversationId, extendParams, extendData, hidden, files })
      .then((response) => {
        const messageReceiver = new MessageReceiver(response.data);

        messageReceiver.receive();

        setIsLoadingMessage(true);

        // 监听接收到消息头事件
        messageReceiver.on(MessageReceiverEvent.HEADER_RECEIVED, (packageObj: IMessagePackage) => {
          let data;
          try {
            data = JSON.parse(packageObj.data) as HeaderMessagePackage;
            // 创建新消息，并设置 isFresh = true;
            const message = createMessage({
              id: data.message_id,
              role: Role.AI,
              content: [],
              status: MessageStatus.Loading,
              agentCode,
            });
            if (!message) return;
            // TODO: 优化陈旧消息判断逻辑
            setMessages((messages) => {
              return [...messages, message];
            });

            const conversationId = data.conversation_id;
            // TODO 切换会话逻辑可以优化
            // 通过当前路由判断是否跳转到对话详情页，chat/:id，跳转后自动切换当前会话
            if (isNewConversation) {
              runner(BuildInCommand.NewConversationCreated, { conversationId });
              runner(BuildInCommand.NavigateTo, { path: `/chat/${conversationId}?isNew=true` });
              setAllState(conversationPreState, conversationId);
            }
          } catch (error) {
            // TODO 异常处理
            console.error("Failed to parse structured data (in progress):", error);
          }
        });

        // 监听消息完成事件
        messageReceiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
          isLoadingMessageRef.current = false;
          // 将最后的消息状态设置为 finished
          setMessages((messages) => {
            const message = messages[messages.length - 1];
            if (!message) {
              return messages;
            }
            message.status = MessageStatus.Finished;
            return [...messages];
          });
          setIsLoadingMessage(false);
        });

        // 监听正在接收消息包事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (packageObj: IMessagePackage) => {
          // 更新对应的消息
          setMessages((messages) => {
            return updateLoadingMessagePackage(messages, packageObj);
          });
        });

        // 监听消息包接收完成事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, () => {
          // 如果没有 loading 状态的
        });

        // 监听消息包异常
        messageReceiver.on(MessageReceiverEvent.ERROR, () => {
          // 如果没有 loading 状态的
        });

        messageReceiver.on(MessageReceiverEvent.DONE, () => {
          setIsLoadingMessage(false);
        });
      })
      .catch((error) => {
        console.error(error);
        cancelStaleChatRequest($transmitter);
      });

    setTransmitter($transmitter);
  };

  useSubscribeCommand(BuildInCommand.SendMessage, sendMessage);

  useSubscribeCommand(BuildInCommand.LoadUnfinishedMessage, (params) => sendMessage(params, true));

  useSubscribeCommand(BuildInCommand.CancelMessageGenerate, () => {
    if (isLoadingMessageRef.current) {
      post("/chat/cancel", {
        conversation_id: activeConversationId,
      }).then(() => {
        cancelStaleChatRequest(transmitter);
      });
    }
  });

  useSubscribeCommand(BuildInCommand.CancelChatRequest, () => {
    cancelStaleChatRequest(transmitter);
    // setMessages((messages) => {
    //   const message = messages[messages.length - 1];
    //   if (!message) {
    //     return messages;
    //   }
    //   message.setError({
    //     message: "请求中断，请重试",
    //     type: MessageErrorType.Terminated,
    //   });
    //   return [...messages];
    // });
  });

  return <>{children}</>;
};

export default AgentCore;
