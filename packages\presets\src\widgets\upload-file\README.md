# File Upload Components

This directory contains components for handling file uploads with real-time progress tracking.

## Components

### FileList

A React component that displays a list of files with their upload status and progress, featuring real upload functionality with axios progress tracking.

#### Features

- **Real Upload Progress Tracking**: Uses axios `onUploadProgress` to track actual file upload progress
- **Dynamic File Type Detection**: Automatically detects file types based on extensions (word, markdown, text, json)
- **File Size Formatting**: Displays human-readable file sizes (B, KB, MB, GB)
- **Visual Progress Display**: Shows real-time upload progress with percentage (e.g., "上传中 ... 45%")
- **Animated Progress Bar**: Smooth progress bar that updates during upload with CSS transitions
- **File Status Management**: Supports three states:
  - `success`: File uploaded successfully
  - `failed`: Upload failed (shows "上传失败")
  - `uploading`: Currently uploading (shows progress)
- **Interactive Actions**:
  - Click failed files to retry upload
  - Remove files with the X button
- **Error Handling**: Comprehensive error handling with automatic status updates
- **Memory Management**: Proper cleanup of file tracking references

#### File Status Display

When a file is uploading:
- Status text shows: "上传中 ... X%" where X is the current percentage
- A blue progress bar visually represents the upload progress
- The progress updates every 200ms with realistic increments

#### Usage

```tsx
import { FileList } from "@cscs-agent/presets";

function App() {
  return (
    <div>
      <FileList />
    </div>
  );
}
```

#### File Item Interface

```typescript
interface FileItem {
  id: string;
  name: string;
  type: "word" | "markdown" | "text" | "json";
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}
```

#### Demo Features

The component includes demo functionality:
- Sample files with different statuses
- Simulated upload progress with realistic timing
- "开始新上传 (演示)" button to trigger new uploads
- Automatic progress simulation for uploading files

#### Styling

The component uses Tailwind CSS classes with:
- Blue color scheme for upload progress (`text-blue-500`, `bg-blue-500`)
- Smooth transitions and animations
- Responsive design with proper spacing
- Visual feedback for different file states

## Implementation Details

### Enhanced Upload Progress Tracking

The `onUploadProgress` method:
- **Real-time Progress**: Uses axios `AxiosProgressEvent` with `loaded` and `total` properties
- **Progress Calculation**: Calculates percentage as `Math.round((loaded / total) * 100)`
- **State Updates**: Updates file progress in real-time without unnecessary re-renders
- **Completion Handling**: Automatically transitions to "success" status at 100% with a 200ms delay
- **Memory Cleanup**: Removes file tracking references after completion or error

### File Upload Process

1. **File Processing**: Each uploaded file gets a unique ID and is added to the file list
2. **Type Detection**: Automatically determines file type based on extension
3. **Size Formatting**: Converts byte sizes to human-readable format
4. **Progress Tracking**: Maps File objects to FileItem IDs for progress updates
5. **Error Handling**: Catches upload errors and updates file status accordingly
6. **Cleanup**: Removes tracking references to prevent memory leaks

### State Management

- Uses React hooks (`useState`, `useRef`, `useCallback`) for optimal performance
- Maintains file list with proper immutable updates using functional state updates
- Handles concurrent uploads independently with unique file tracking
- Preserves file order and IDs throughout the upload process
- Uses `useCallback` for `onUploadProgress` to prevent unnecessary re-renders

### Technical Features

- **File Tracking**: Uses `Map<File, string>` to track uploads by file reference
- **Unique IDs**: Generates unique file IDs using timestamp and random strings
- **Progress Debouncing**: Efficient state updates without causing UI lag
- **Error Recovery**: Comprehensive error handling with status updates
- **Timeout Handling**: 60-second timeout for file uploads

## API Integration

The component integrates with the `/rag/files/upload` endpoint using:
- **FormData**: Proper multipart/form-data encoding
- **Progress Events**: Real-time upload progress tracking
- **Error Handling**: Comprehensive error catching and user feedback
- **Timeout Configuration**: Configurable upload timeouts

## Future Enhancements

Potential improvements could include:
- Drag and drop functionality
- File type validation and restrictions
- Upload cancellation with AbortController
- Batch upload operations with queue management
- Upload resume functionality
- File preview capabilities
- Custom upload endpoints configuration
