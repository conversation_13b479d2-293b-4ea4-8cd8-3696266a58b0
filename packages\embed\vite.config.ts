import { resolve } from "path";

import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";

import { cscsAgentCssConfig, cssConsolidator } from "./vite-plugins/css-consolidator";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    typescript({
      tsconfig: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
    }),
    tailwindcss(),
    svgr({
      include: "**/*.svg?react",
    }),
    cssConsolidator(cscsAgentCssConfig),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/embed",
      fileName: (format) => `index.${format}.js`,
      formats: ["es"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      external: [
        "react",
        "react-dom",
        "antd",
        "@ant-design/icons",
        "@cscs-agent/core",
        "@cscs-agent/icons",
        "@cscs-agent/presets",
      ],
      output: {},
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
}));
