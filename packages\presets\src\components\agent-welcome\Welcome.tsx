import React from "react";

import { getAssetUrl, useActiveAgentConfig } from "@cscs-agent/core";

const Welcome: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <div className="pts:p-4">
      <img width="64px" src={getAssetUrl(agentConfig?.logo)} className="pts:block pts:mx-auto pts:mb-6" />
      <div className="pts:font-bold pts:text-black-85 pts:text-2xl pts:text-center">{agentConfig?.welcome}</div>
      {agentConfig?.description && (
        <div className="pts:mt-4 pts:rounded-lg pts:w-[800px] pts:text-black-65 pts:text-sm pts:text-center">
          {agentConfig?.description}
        </div>
      )}
    </div>
  );
};

export default Welcome;
