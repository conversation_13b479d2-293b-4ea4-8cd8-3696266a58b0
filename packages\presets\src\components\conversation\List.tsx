import { Button, Dropdown } from "antd";
import { ItemType } from "antd/lib/menu/hooks/useItems";
import React, { useMemo } from "react";

import { EllipsisOutlined } from "@ant-design/icons";

interface ListProps {
  items: ListItem[];
  groupSorter: (a: string, b: string) => number;
  menu: (item: ListItem) => { items: ItemType[] };
  activeKey?: string;
  onActiveChange: (key: string) => void;
}

interface ListItem {
  key: string;
  label: React.ReactNode;
  group: string;
  [key: string]: any;
}

const List: React.FC<ListProps> = (props) => {
  const { items, groupSorter, menu, activeKey, onActiveChange } = props;

  const groupItems = useMemo(() => {
    const listMap = new Map<string, ListItem[]>();

    for (const item of items) {
      const groupName = item.group;
      if (!listMap.get(groupName)) {
        listMap.set(groupName, []);
      }
      listMap.get(groupName)?.push(item);
    }

    const groups = Array.from(listMap.entries());

    groups.sort((a, b) => groupSorter(a[0], b[0]));

    return groups.map((group) => ({
      label: group[0],
      list: group[1],
    }));
  }, [items, groupSorter]);

  return (
    <>
      {groupItems.map((group) => (
        <div key={group.label}>
          <div className="pts:py-2 pts:pl-4 pts:text-black-65 pts:text-xs">{group.label}</div>
          <ul className="pts:pl-0">
            {group.list.map((item) => (
              <li
                key={item.key}
                className={`pts:flex pts:items-center pts:px-4 pts:hover:bg-[rgba(0,0,0,0.05)] pts:py-1.5 pts:text-sm pts:cursor-pointer ${activeKey === item.key ? "pts:bg-[rgba(49,186,244,0.10)]" : ""}`}
                onClick={() => onActiveChange(item.key)}
              >
                <div className="pts:flex-1 pts:pr-2 pts:overflow-hidden pts:text-ellipsis pts:text-nowrap">
                  {item.label}
                </div>
                <Dropdown menu={menu(item)} placement="bottom">
                  <Button
                    type="text"
                    size="small"
                    icon={<EllipsisOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  ></Button>
                </Dropdown>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </>
  );
};

export default List;
