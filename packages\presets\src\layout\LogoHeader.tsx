import { But<PERSON>, Toolt<PERSON> } from "antd";
import React from "react";

import { getAssetUrl, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PresetsCommand } from "../command";

const LogoHeader: React.FC = () => {
  const runner = useCommandRunner();

  const closeSideBar = () => {
    runner(PresetsCommand.CloseSideBar);
  };

  return (
    <div className="pts:flex pts:justify-between pts:items-center pts:mx-6 pts:my-2 pts:h-8">
      <div className="pts:flex pts:items-center">
        <img src={getAssetUrl("/assets/logo.png")} width="32" />
        <div className="pts:ml-3 pts:font-bold pts:text-l pts:text-base">CSCS AI助手</div>
      </div>
      <Tooltip title="隐藏侧边栏">
        <Button type="text" size="small" icon={<Icon icon="SideBar" />} onClick={closeSideBar} />
      </Tooltip>
    </div>
  );
};

export default LogoHeader;
