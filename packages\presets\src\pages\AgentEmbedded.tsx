import React, { useContext, useEffect } from "react";

import NavigationB<PERSON> from "@/components/navigation-bar";
import { DefaultAgentLayout } from "@/layout";
import {
  AgentChatContext,
  BuildInCommand,
  Sender,
  useActiveAgentCode,
  useActiveAgentMenuCode,
  useCommandRunner,
} from "@cscs-agent/core";

interface AgentHomeEmbeddedProps {
  agentCode: string;
}

const AgentHomeEmbedded: React.FC<AgentHomeEmbeddedProps> = (props) => {
  const { agentCode } = props;
  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const { config } = useContext(AgentChatContext);
  const runner = useCommandRunner();

  useEffect(() => {
    if (agentCode === activeAgentMenuCode) return;
    const agents = config.agents ?? [];
    const code = agents.find((i) => i.code === agentCode)?.code;
    if (code) {
      setActiveAgentMenuCode(code);
      setActiveAgentCode(code);
    }
  }, [agentCode, config.agents]);

  useEffect(() => {
    const search = new URLSearchParams(window.location.search);
    const sendMessage = search.get("sendMessage");
    if (sendMessage) {
      setTimeout(() => {
        runner(BuildInCommand.SendMessage, { message: sendMessage, isNewConversation: true, agentCode: agentCode });
      }, 100);
    }
    return () => {
      setActiveAgentMenuCode(null);
      setActiveAgentCode(null);
    };
  }, []);

  return <DefaultAgentLayout sender={<Sender isNewConversation={true} />} navigationBar={<NavigationBar />} />;
};

export default AgentHomeEmbedded;
