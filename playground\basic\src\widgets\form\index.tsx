import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Picker, Form, Input, InputNumber, Mentions, Select, TreeSelect } from "antd";
import React from "react";

import { BuildInCommand, useActiveAgentCode, useCommandRunner } from "@cscs-agent/core";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const FormDemo: React.FC = () => {
  const [form] = Form.useForm();
  const runner = useCommandRunner();
  const [activeAgentCode] = useActiveAgentCode();

  return (
    <Form
      {...formItemLayout}
      form={form}
      style={{ maxWidth: 600 }}
      initialValues={{ variant: "filled" }}
      onFinish={(values) => {
        runner(BuildInCommand.SendMessage, {
          message: JSON.stringify(values),
          agentCode: activeAgentCode,
        });
      }}
    >
      <Form.Item label="Input" name="Input">
        <Input />
      </Form.Item>

      <Form.Item label="InputNumber" name="InputNumber">
        <InputNumber style={{ width: "100%" }} />
      </Form.Item>

      <Form.Item label="TextArea" name="TextArea">
        <Input.TextArea />
      </Form.Item>

      <Form.Item label="Mentions" name="Mentions">
        <Mentions />
      </Form.Item>

      <Form.Item label="Select" name="Select">
        <Select />
      </Form.Item>

      <Form.Item label="Cascader" name="Cascader">
        <Cascader />
      </Form.Item>

      <Form.Item label="TreeSelect" name="TreeSelect">
        <TreeSelect />
      </Form.Item>

      <Form.Item label="DatePicker" name="DatePicker">
        <DatePicker />
      </Form.Item>

      <Form.Item label="RangePicker" name="RangePicker">
        <RangePicker />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
};

export default FormDemo;
