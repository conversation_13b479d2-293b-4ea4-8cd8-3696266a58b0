import { Space } from "antd";
import React from "react";

import { useActiveAgentConfig } from "@/core";
import { Role } from "@/types";

const Footer: React.FC<{ role: Role }> = (props) => {
  const { role } = props;
  const agentConfig = useActiveAgentConfig();

  const widgets = agentConfig?.message?.slots?.footer?.widgets ?? [];

  if (widgets.length === 0) return null;

  return (
    <Space className="ag:text-black-65">
      {widgets.map((Widget) => {
        if (!Widget.role || Widget.role === role) {
          return <Widget.component key={Widget.code} {...Widget.props} />;
        }
      })}
    </Space>
  );
};

export default Footer;
