import { StateUpdateStrategy } from "@/types";

interface StateSetOption {
  messageId?: string | null;
  strategy: StateUpdateStrategy;
  path: string;
  value: any;
}

function transformValueToString(value: any) {
  try {
    const str = JSON.stringify(value);
    return str.replace(/^"|"$/g, "");
  } catch {
    console.error("transformValueToString error:", value);
    return "";
  }
}

export function createStateSet(items: StateSetOption[]) {
  const setList = items.reduce((prev, current) => {
    const { messageId, strategy, path, value } = current;

    const scopeTag = messageId ? `<scope>@message/${messageId}</scope>` : "";
    const valueStr = transformValueToString(value);
    const setStr = `
<set>
  ${scopeTag}
  <strategy>${strategy}</strategy>
  <path>${path}</path>
  <value>${valueStr}</value> 
</set>`;
    return prev + "\n" + setStr;
  }, "");

  const stateSetString = `
<message-embedded>
  <state>
    ${setList}
  </state>
</message-embedded>`;

  return stateSetString;
}
