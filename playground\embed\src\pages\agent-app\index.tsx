import React, { useEffect, useRef } from "react";
import { createMemoryRouter } from "react-router-dom";

import { RouterConfig } from "@cscs-agent/core";
import { AgentAppEmbedded } from "@cscs-agent/embed";
import { AgentHome, Chat, ConversationHistory, Login, defaultAuthGuard } from "@cscs-agent/presets";

import { config } from "../../agent-config";
import SoloMessage from "../solo-message";
import Home from "./Home";

const routerConfig: RouterConfig = {
  pages: {
    home: {
      Component: Home,
    },
    chat: {
      Component: Chat,
    },
    agentHome: {
      Component: AgentHome,
    },
    conversationHistory: {
      Component: ConversationHistory,
    },
    login: {
      enable: true,
      Component: Login,
    },
  },
  authGuard: defaultAuthGuard,
  rootRoutes: [
    {
      path: "message/test-solo",
      Component: SoloMessage,
      auth: true,
    },
  ],
};

const AgentApp = () => {
  const appRef = useRef<{
    router: ReturnType<typeof createMemoryRouter>;
  }>(null);

  useEffect(() => {
    // appRef.current?.router.navigate("/chat/7a9444d3e1b94c5f94f8939240a79264");
  }, []);

  return (
    <div
      style={{
        height: "calc(100vh - 150px)",
      }}
    >
      <AgentAppEmbedded agentChatConfig={config} routerConfig={routerConfig} ref={appRef} />
    </div>
  );
};

export default AgentApp;
