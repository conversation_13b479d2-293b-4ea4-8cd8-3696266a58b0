import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderFooterOutside: React.FC = () => {
  const agentConfig = useActiveAgentConfig();
  const direction = agentConfig?.sender?.slots?.footer?.direction ?? "ltr";

  const widgets = useMemo(() => {
    const widgets = agentConfig?.sender?.slots?.footer?.widgets ?? [];
    return widgets.filter((i) => i.placement === "outside");
  }, [agentConfig]);

  return (
    <div className={`ag:pt-2 ag:flex ag:flex-wrap ${direction === "rtl" ? "ag:flex-row-reverse" : ""}`}>
      {widgets.map((Widget, index) => (
        <div className="ag:mr-2" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderFooterOutside;
