import React, { useMemo } from "react";
import { BlockMath, InlineMath } from "react-katex";

const Math: React.FC<{ className?: string; content: string }> = (props) => {
  const { className, content } = props;
  const isInline = className?.includes("inline");

  const mathStr = useMemo(() => {
    try {
      let str = content?.replace(/&#92;/g, "\\");
      str = str?.replace(/^```math\n/, "").replace(/\n```$/, "") ?? "";
      return str;
    } catch (error) {
      console.error(error);
      return "";
    }
  }, [content]);

  return isInline ? <InlineMath math={mathStr} /> : <BlockMath math={mathStr} />;
};

export default Math;
