import { useDebounceFn } from "ahooks";
import mermaid from "mermaid";
import React, { useEffect, useRef } from "react";

mermaid.initialize({
  startOnLoad: false,
  theme: "default",
  securityLevel: "loose",
  suppressErrorRendering: true,
});

interface MermaidRenderProps {
  content: string;
}

const MermaidRender: React.FC<MermaidRenderProps> = ({ content }) => {
  const [diagram, setDiagram] = React.useState<string | null>(null);
  const id = useRef(`mermaid-${Math.random().toString(36).substring(2, 11)}`);

  const { run: renderMermaid } = useDebounceFn(
    () => {
      const _content = content.replace(/^```mermaid\n/, "").replace(/\n```$/, "");
      mermaid
        .parse(_content, { suppressErrors: true })
        .then((result) => {
          if (!result) {
            return;
          }
          return mermaid.render(id.current, _content);
        })
        .then((result) => {
          if (result) {
            setDiagram(result.svg);
          }
        });
    },
    { wait: 100 },
  );

  useEffect(() => {
    renderMermaid();
  }, [content]);

  return (
    <>
      <div className="ag:my-4" hidden={!diagram}>
        <div
          dangerouslySetInnerHTML={{ __html: diagram ?? "" }}
          className="ag:max-w-full ag:overflow-auto"
          style={{ lineHeight: "normal" }}
        />
      </div>
      <pre hidden={!!diagram}>{content}</pre>
    </>
  );
};

export default MermaidRender;
