/**
 * LLM-based condition evaluator for coreFile entries
 * Uses @openai/agents to dynamically evaluate natural language conditions
 */

import { Agent, MCPServerStdio, run } from "@openai/agents";

import type { ConditionEvaluationResult } from "../types.js";
import { Logger } from "./logger.js";
import { createModel } from "./model.js";

export class ConditionEvaluator {
  private agent?: Agent;
  private fileSystemMCP: MCPServerStdio;

  constructor(workspace?: string) {
    const dir = workspace ?? process.cwd();
    this.fileSystemMCP = new MCPServerStdio({
      name: "Filesystem MCP Server, via npx",
      fullCommand: `npx -y @modelcontextprotocol/server-filesystem ${dir}`,
      cacheToolsList: true,
    });
  }

  async init(apiKey?: string) {
    await this.fileSystemMCP.connect();

    // Initialize the LLM agent for condition evaluation only if API key is available
    if (apiKey ?? process.env.OPENAI_API_KEY) {
      this.agent = new Agent({
        name: "ConditionEvaluator",
        instructions: `You are an expert code analyzer specializing in evaluating conditions for file operations in Agent projects.
        Your task is to evaluate natural language conditions against the current state of a codebase and determine whether they are satisfied.

        When evaluating conditions:
        1. Analyze the provided file contents carefully
        2. Look for specific patterns, imports, exports, or code structures mentioned in the condition
        3. Consider the context of the entire codebase when making decisions
        4. Provide clear reasoning for your evaluation

        Always respond with a JSON object containing:
        - "satisfied": boolean indicating if the condition is met
        - "reasoning": detailed explanation of your evaluation
        - "filesAnalyzed": array of file paths that were analyzed

        Be precise and thorough in your analysis. Consider edge cases and provide detailed reasoning.`,
        model: createModel(),
        mcpServers: [this.fileSystemMCP],
        toolUseBehavior: "run_llm_again",
        resetToolChoice: true,
      });
    }
  }

  /**
   * Evaluate a condition against the current codebase state
   */
  async evaluateCondition(condition: string): Promise<ConditionEvaluationResult> {
    if (!this.agent) {
      Logger.warning("LLM condition evaluation not available without OpenAI API key. Defaulting to not satisfied.");
      return {
        condition,
        satisfied: false,
        reasoning: "LLM evaluation not available",
      };
    }

    try {
      // Build the evaluation prompt
      const prompt = this.buildEvaluationPrompt(condition);

      // Execute LLM evaluation
      const result = await run(this.agent, prompt);

      if (!result.finalOutput || typeof result.finalOutput !== "string") {
        throw new Error("LLM did not return valid evaluation result");
      }

      // Parse the JSON response
      const evaluation = this.parseEvaluationResponse(result.finalOutput);
      await this.fileSystemMCP.close();
      return {
        condition,
        satisfied: evaluation.satisfied,
        reasoning: evaluation.reasoning,
      };
    } catch (error) {
      Logger.warning(
        `Condition evaluation failed for "${condition}": ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      await this.fileSystemMCP.close();
      // Default to satisfied on error to avoid blocking file operations
      return {
        condition,
        satisfied: false,
        reasoning: `Evaluation failed, defaulting to satisfied: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }

  /**
   * Build the evaluation prompt for the LLM
   */
  private buildEvaluationPrompt(condition: string): string {
    return `Evaluate the following condition: 
    
    CONDITION: ${condition}.

Please using tools to read files in CONDITION, analyze the CONDITION against the file contents and determine if it is satisfied.
Only call one tool every time.

Respond with a JSON object in this exact format:
{
  "satisfied": boolean,
  "reasoning": "detailed explanation of your evaluation",
  "filesAnalyzed": ["array", "of", "file", "paths", "analyzed"]
}`;
  }

  /**
   * Parse the LLM evaluation response
   */
  private parseEvaluationResponse(response: string): {
    satisfied: boolean;
    reasoning: string;
    filesAnalyzed?: string[];
  } {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      if (typeof parsed.satisfied !== "boolean" || typeof parsed.reasoning !== "string") {
        throw new Error("Invalid response format");
      }

      return parsed;
    } catch (error) {
      Logger.warning(`Failed to parse LLM evaluation response: ${error}`);

      // Fallback: try to determine satisfaction from response text
      const satisfied =
        response.toLowerCase().includes("satisfied") ||
        response.toLowerCase().includes("true") ||
        !response.toLowerCase().includes("not satisfied");

      return {
        satisfied,
        reasoning: `Failed to parse structured response. Raw response: ${response}`,
        filesAnalyzed: [],
      };
    }
  }
}
