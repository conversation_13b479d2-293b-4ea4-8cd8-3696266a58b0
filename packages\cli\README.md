# @cscs-agent/cli

✨ **Powered by AI** - A powerful CLI tool for creating and managing CSCS Agent projects with AI-enhanced features including intelligent widget generation, project upgrades, and comprehensive development workflows.

## Features

- 🚀 **Template-based project creation** - Create projects from predefined templates
- 🤖 **AI-powered widget generation** - Generate custom widget components with intelligent code analysis
- 🔄 **Intelligent project upgrades** - Upgrade existing projects with LLM-driven file comparison and updates
- 🎯 **Interactive mode** - User-friendly prompts for all commands with guided workflows
- 🔍 **Template discovery** - Automatic discovery and validation of available templates
- 📦 **Smart package manager detection** - Automatically detects and uses pnpm, yarn, or npm
- ✅ **Project validation** - Validates project names and prevents conflicts
- 🎨 **Rich CLI experience** - Colored output, progress indicators, and clear feedback
- 🌍 **Cross-platform support** - Works on Windows, macOS, and Linux
- 🛠️ **Comprehensive error handling** - Detailed error messages and recovery suggestions

## Installation

### Global Installation (Recommended)

```bash
npm install -g @cscs-agent/cli
```

### Using npx (No Installation Required)

```bash
npx @cscs-agent/cli create my-project
```

## Usage

### Quick Start

Create a new project with the default template:

```bash
cscs-agent-cli create my-project
```

### Interactive Mode

Launch interactive mode for guided project creation:

```bash
cscs-agent-cli create --interactive
```

### Specify Template

Create a project with a specific template:

```bash
cscs-agent-cli create my-project --template basic
```

### Advanced Options

```bash
cscs-agent-cli create my-project \
  --template basic \
  --directory ./projects/my-project \
  --package-manager pnpm \
  --skip-install
```

## Commands

### `create [project-name]`

Create a new CSCS Agent project from templates.

**Options:**
- `-t, --template <template>` - Template to use (default: basic)
- `-d, --directory <directory>` - Target directory (default: project name)
- `--skip-install` - Skip dependency installation
- `--package-manager <manager>` - Package manager to use (npm, yarn, pnpm)
- `-i, --interactive` - Run in interactive mode

**Examples:**
```bash
# Basic usage
cscs-agent-cli create my-app

# With specific template
cscs-agent-cli create my-app --template basic

# Interactive mode
cscs-agent-cli create --interactive

# Skip dependency installation
cscs-agent-cli create my-app --skip-install

# Use specific package manager
cscs-agent-cli create my-app --package-manager yarn
```

### `generate` / `g`

✨ Generate a basic widget component using AI-powered code generation.

**Options:**
- `-n, --name <name>` - Widget name (PascalCase, required)
- `-p, --target-path <path>` - Target project path (default: current directory)
- `-d, --description <description>` - Widget description for AI generation
- `--props <props>` - Widget props as JSON string
- `--placement <placement>` - Widget placement (message, sender, sidePanel)
- `--slot <slot>` - Widget slot (blocks, header, footer, headerPanel, render)
- `-i, --interactive` - Run in interactive mode

**Examples:**
```bash
# Interactive mode (recommended)
cscs-agent-cli generate --interactive

# Generate with specific options
cscs-agent-cli generate --name MyWidget --placement message --slot blocks

# Generate with description for AI
cscs-agent-cli generate --name UserCard --description "A card component to display user information"

# Generate with custom props
cscs-agent-cli generate --name DataTable --props '{"data": "array", "columns": "array"}'
```

### `templates`

List all available templates with descriptions.

```bash
cscs-agent-cli templates
```

### `upgrade`

✨ Upgrade an agent project using AI-powered file comparison and intelligent updates (Experimental).

This command uses LLM-driven analysis to compare your project files with the latest template and intelligently merge updates while preserving your customizations.

**Options:**
- `-p, --target-path <path>` - Target project path (default: current directory)
- `-i, --interactive` - Run in interactive mode with guided prompts
- `--skip-backup` - Skip creating backup files before upgrade
- `--dry-run` - Preview changes without applying them
- `--force` - Force upgrade even if there are warnings

**Features:**
- 🤖 **LLM-powered file analysis** - Intelligent comparison of project files
- 🔄 **Smart merging** - Preserves customizations while applying updates
- 📋 **Diff visualization** - Shows exactly what will change before applying
- 🛡️ **Backup creation** - Automatic backup of modified files
- 🎯 **Condition evaluation** - Smart decision making for file operations

**Examples:**
```bash
# Interactive upgrade (recommended)
cscs-agent-cli upgrade --interactive

# Preview changes without applying
cscs-agent-cli upgrade --dry-run

# Upgrade specific project
cscs-agent-cli upgrade --target-path ./my-project

# Force upgrade with no backups (use with caution)
cscs-agent-cli upgrade --force --skip-backup
```

### `info`

Show CLI information including version, platform details, and available commands.

```bash
cscs-agent-cli info
```

## AI-Powered Features

### Widget Generation

The `generate` command uses advanced AI to create custom widget components:

- **Intelligent Code Analysis** - Analyzes your project structure and existing patterns
- **Context-Aware Generation** - Generates code that fits your project's architecture
- **Multiple Placement Options** - Supports message, sender, and sidePanel placements
- **Flexible Slot System** - Works with blocks, header, footer, headerPanel, and render slots
- **Custom Props Support** - Generates TypeScript interfaces for your widget props

### Project Upgrades

The `upgrade` command leverages LLM capabilities for intelligent project updates:

- **File Comparison** - AI analyzes differences between your project and latest templates
- **Smart Merging** - Preserves your customizations while applying necessary updates
- **Condition Evaluation** - Uses natural language conditions for upgrade decisions
- **Diff Visualization** - Shows exactly what changes will be made before applying

## Templates

Templates are located in the `templates/` directory within the CLI package. Each template contains a complete project structure ready for development.

### Available Templates

- **basic** - A comprehensive CSCS Agent application with essential features, configurations, and modern development setup

### Template Structure

Each template includes:

```
template-name/
├── package.json          # Project metadata and dependencies
├── index.html           # Main HTML file
├── eslint.config.js     # ESLint configuration
├── tsconfig.json        # TypeScript configuration
├── tsconfig.node.json   # Node.js TypeScript configuration
├── src/
│   ├── main.tsx         # Application entry point
│   ├── agent-config.tsx # Agent configuration
│   └── pages/           # Application pages
├── config/              # Configuration files
├── mock/                # Mock data for development
└── public/              # Static assets
```

## Configuration

The CLI automatically detects and uses the best available package manager:

1. **pnpm** (preferred)
2. **yarn** (fallback)
3. **npm** (default fallback)

You can override this behavior using the `--package-manager` option.

## Error Handling

The CLI provides comprehensive error handling for common scenarios:

- **Invalid project names** - Validates npm package name format
- **Existing directories** - Prevents overwriting existing projects
- **Missing templates** - Lists available templates when specified template is not found
- **Network issues** - Graceful handling of dependency installation failures
- **Permission errors** - Clear guidance for permission-related issues

## Development

### Building from Source

```bash
# Clone the repository
git clone <repository-url>
cd agent/packages/cli

# Install dependencies
pnpm install

# Build the CLI
pnpm run build

# Test the CLI
pnpm run test

# Test CLI functionality
pnpm run test:cli
```

### Project Structure

```
packages/cli/
├── src/
│   ├── cli.ts                    # Main CLI entry point with command definitions
│   ├── types.ts                  # Type definitions and interfaces
│   ├── logo.ts                   # CLI branding and logo
│   ├── commands/
│   │   ├── create.ts             # Project creation command
│   │   ├── generate.ts           # AI-powered widget generation
│   │   └── upgrade/
│   │       ├── upgrade.ts        # LLM-driven project upgrade
│   │       └── files.ts          # File upgrade configurations
│   └── utils/
│       ├── logger.ts             # Enhanced logging utilities
│       ├── model.ts              # LLM model configuration
│       ├── project-analyzer.ts   # Project structure analysis
│       ├── widget-generator.ts   # AI widget generation logic
│       ├── streamlined-file-upgrader.ts  # File upgrade engine
│       ├── condition-evaluator.ts        # LLM condition evaluation
│       ├── dependency-updater.ts         # Dependency management
│       └── filesystem-manager.ts         # File system operations
├── bin/
│   └── cli.js                    # Compiled CLI entry point
├── lib/                          # Compiled TypeScript output
├── templates/                    # Project templates
├── docs/                         # Documentation and guides
├── examples/                     # Usage examples and demos
└── test/                         # Test files and projects
```

### Adding New Templates

1. Create a new directory in `templates/`
2. Add a complete project structure
3. Ensure `package.json` exists with proper metadata
4. Test the template with the CLI

Example template `package.json`:

```json
{
  "name": "my-template",
  "version": "1.0.0",
  "description": "My custom CSCS Agent template",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite --force",
    "build": "vite build",
    "lint": "eslint ./src --ext .ts,.tsx --fix",
    "preview": "vite preview"
  },
  "dependencies": {
    // ... your dependencies
  }
}
```

### Environment Configuration

The CLI requires certain environment variables for AI features:

```bash
# .env file
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here  # Optional, for alternative LLM
```

### Testing AI Features

```bash
# Test widget generation
cscs-agent-cli generate --name TestWidget --interactive

# Test upgrade functionality
cscs-agent-cli upgrade --dry-run --target-path ./test-project

# Test with debug output
DEBUG=* cscs-agent-cli generate --name DebugWidget
```

## Troubleshooting

### Common Issues

1. **"Templates directory not found"**
   - Ensure the CLI package is properly installed
   - Check that `templates/` directory exists in the CLI package

2. **"CLI module not found"**
   - Run `pnpm run build` in the CLI package directory
   - Ensure TypeScript compilation completed successfully

3. **"Permission denied"**
   - On Unix systems, ensure the CLI binary is executable
   - On Windows, run as administrator if needed

4. **"Project name validation failed"**
   - Use lowercase letters, numbers, hyphens, and underscores only
   - Ensure the name doesn't start with a number or special character

5. **"AI features not working"**
   - Ensure `OPENAI_API_KEY` is set in your environment
   - Check your API key has sufficient credits and permissions
   - Verify network connectivity for API calls

6. **"Widget generation failed"**
   - Ensure you're in a valid CSCS Agent project directory
   - Check that the widget name follows PascalCase convention
   - Verify the target path exists and is writable

7. **"Upgrade command errors"**
   - Ensure you're in a valid project directory with `package.json`
   - Check that backup directory is writable (if not using `--skip-backup`)
   - Try using `--dry-run` first to preview changes

### Debug Information

Get debug information:

```bash
# Check CLI version and info
cscs-agent-cli info

# List available templates
cscs-agent-cli templates

# Test with verbose output
DEBUG=* cscs-agent-cli create test-project

# Test AI features with debug
DEBUG=* cscs-agent-cli generate --name TestWidget --interactive
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the ISC License. See the main project LICENSE file for details.

## Support

For issues and questions:

- Check the [troubleshooting section](#troubleshooting)
- Review existing issues in the repository
- Create a new issue with detailed information about your problem

## Changelog

### v0.4.12 (Current)

- ✨ **AI-Powered Widget Generation** - Generate custom widget components using LLM
- 🔄 **Intelligent Project Upgrades** - LLM-driven file comparison and smart merging
- 🤖 **Enhanced Interactive Mode** - Improved prompts and guided workflows
- 📋 **Diff Visualization** - Preview changes before applying upgrades
- 🛡️ **Backup System** - Automatic backup creation during upgrades
- 🎯 **Condition Evaluation** - Smart decision making for file operations
- 📊 **Project Analysis** - Intelligent analysis of project structure and patterns
- 🔧 **Streamlined File Upgrader** - Advanced file upgrade engine with LLM integration

### v0.3.0

- Initial release with enhanced CLI functionality
- Interactive mode support
- Template discovery and validation
- Cross-platform compatibility
- Comprehensive error handling
- Rich CLI experience with colored output
