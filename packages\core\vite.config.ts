import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

import { defineConfig } from "vite";
import dts from "vite-plugin-dts";
import { viteStaticCopy } from "vite-plugin-static-copy";
import svgr from "vite-plugin-svgr";

import tailwindcss from "@tailwindcss/vite";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    tailwindcss(),
    dts({
      tsconfigPath: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
      exclude: ["**/__tests__/**", "**/mock/**"],
    }),
    svgr({
      include: "**/*.svg?react",
    }),
    viteStaticCopy({
      targets: [
        {
          src: "src/theme/*.css",
          dest: "theme",
        },
      ],
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/core",
      fileName: (format) => `index.${format}.js`,
      formats: ["es"],
    },
    sourcemap: mode === "development",
    target: "es2015",
    outDir: "dist",
    cssCodeSplit: true,
    cssTarget: "chrome96",
    cssMinify: true,
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      // Dependencies from package.json
      external: [
        // Dependencies
        "@cscs-agent/icons",
        "dot-prop",
        "slate",
        "slate-react",
        "slate-history",
        "slate-hyperscript",
        "ahooks",
        // "mermaid",
        "moment",
        "nanoid",
        "penpal",
        "fast-xml-parser",
        "eventemitter3",
        "jotai",
        "react-copy-to-clipboard",
        "react-infinite-scroll-component",
        "react-markdown",
        // "rehype-highlight",
        // "rehype-raw",
        // "remark-gfm",
        "remark-math",
        "handlebars",
        // "rehype-katex",
        // Peer dependencies
        "@ant-design/icons",
        "antd",
        "react",
        // "react-router-dom",
      ],
      output: {
        assetFileNames: "core-tailwind.css",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
    devSourcemap: true,
  },
}));
