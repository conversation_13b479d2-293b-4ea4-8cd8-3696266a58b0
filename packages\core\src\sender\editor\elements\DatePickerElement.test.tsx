import React from "react";
import { describe, expect, it, vi } from "vitest";

import { render, screen } from "@testing-library/react";

import { DatePickerElement } from "./DatePickerElement";

// Mock Ant Design components
vi.mock("antd", () => ({
  DatePicker: ({ placeholder, disabled }: any) => (
    <input data-testid="datepicker" placeholder={placeholder} disabled={disabled} />
  ),
  Tooltip: ({ children, title }: any) => (
    <div data-testid="tooltip" title={title}>
      {children}
    </div>
  ),
}));

// Mock slate-react
vi.mock("slate-react", () => ({
  RenderElementProps: {},
}));

// Mock moment
vi.mock("moment", () => ({
  default: vi.fn(() => null),
}));

describe("DatePickerElement", () => {
  const mockElement = {
    placeholder: "请选择日期",
    defaultValue: "",
    tooltips: "选择一个日期",
    disabled: false,
    format: "YYYY-MM-DD",
    showTime: false,
    allowClear: true,
  } as any;

  it("renders without crashing", () => {
    render(<DatePickerElement element={mockElement} {...({} as any)} />);

    const datePicker = screen.getByTestId("datepicker");
    expect(datePicker).toBeTruthy();
  });

  it("renders with correct placeholder", () => {
    render(<DatePickerElement element={mockElement} {...({} as any)} />);

    const datePicker = screen.getByTestId("datepicker");
    expect(datePicker.getAttribute("placeholder")).toBe("请选择日期");
  });

  it("renders disabled state", () => {
    const disabledElement = {
      ...mockElement,
      disabled: true,
    };

    render(<DatePickerElement element={disabledElement} {...({} as any)} />);

    const datePicker = screen.getByTestId("datepicker");
    expect(datePicker.hasAttribute("disabled")).toBe(true);
  });

  it("renders with tooltip", () => {
    render(<DatePickerElement element={mockElement} {...({} as any)} />);

    const tooltip = screen.getByTestId("tooltip");
    expect(tooltip.getAttribute("title")).toBe("选择一个日期");
  });
});
