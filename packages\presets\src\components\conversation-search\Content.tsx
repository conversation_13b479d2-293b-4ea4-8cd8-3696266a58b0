import React, { useMemo } from "react";

import Highlight from "./Highlight";

interface ContentProps {
  keyword: string;
  content: string;
}

const Content: React.FC<ContentProps> = (props) => {
  const { keyword, content } = props;

  const fragment = useMemo(() => {
    const regex = new RegExp(keyword, "i");
    const firstMatchPosition = content.search(regex) ?? 0;
    const start = firstMatchPosition > 10 ? firstMatchPosition - 10 : 0;
    const end = start + keyword.length + 80;
    const prefix = start > 0 ? "..." : "";
    const suffix = end < content.length ? "..." : "";
    return prefix + content.slice(start, end) + suffix;
  }, [content, keyword]);

  return (
    <div className="pts:overflow-hidden pts:text-nowrap pts:text-ellipsis">
      <Highlight text={fragment} keyword={keyword} />
    </div>
  );
};

export default Content;
