# Dynamic Proxy Configuration

The `getProxyConfig()` method in the Igniter class provides dynamic loading and compilation of TypeScript proxy configuration files at runtime.

## Features

- **Dynamic TypeScript Compilation**: Automatically compiles TypeScript proxy configuration files using esbuild
- **Multiple Search Locations**: Searches common locations for proxy.ts files
- **Caching**: Caches compiled results to avoid recompilation on subsequent calls
- **Error Handling**: Comprehensive error handling for missing files, compilation errors, and invalid configurations
- **Validation**: Validates proxy configuration structure and target URLs

## Usage

```typescript
import { Igniter } from "@cscs-agent/builder";

const igniter = new Igniter(process.cwd());

// Load proxy configuration from default locations
const proxyConfig = await igniter.getProxyConfig();

// Load from specific path
const proxyConfig = await igniter.getProxyConfig("./custom/proxy.ts");

// Disable caching
const proxyConfig = await igniter.getProxyConfig(undefined, false);
```

## Search Locations

The method searches for `proxy.ts` files in the following locations (in order):

1. Provided `configPath` parameter
2. `{root}/proxy.ts`
3. `{root}/config/proxy.ts`
4. `{root}/src/config/proxy.ts`
5. `{cwd}/proxy.ts`
6. `{cwd}/config/proxy.ts`

## Proxy Configuration File Format

Create a `proxy.ts` file with the following structure:

```typescript
import { CommonServerOptions } from "vite";

const config: CommonServerOptions["proxy"] = {
  "^/api": {
    target: "http://localhost:8000",
    changeOrigin: true,
  },
  "/cluster-api": {
    target: "http://localhost:8002",
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/cluster-api/, "/api"),
  },
};

export default config;
```

## Integration with Vite Server

The proxy configuration is automatically integrated with the Vite development server:

```typescript
async start() {
  // Load proxy configuration
  const proxyConfig = await this.getProxyConfig();
  
  const server = await createServer({
    root: resolve(this.root),
    forceOptimizeDeps: true,
    ...baseConfig,
    server: {
      ...baseConfig.server,
      proxy: proxyConfig || baseConfig.server?.proxy,
    },
  });

  await server.listen();
}
```

## Error Handling

The method handles various error scenarios:

- **File Not Found**: Returns `null` if no proxy configuration file is found
- **Compilation Errors**: Throws detailed error messages for TypeScript compilation failures
- **Invalid Configuration**: Validates proxy configuration structure and throws errors for invalid configurations
- **Invalid URLs**: Validates target URLs and throws errors for malformed URLs

## Caching

Compiled proxy configurations are cached based on file modification time. The cache is automatically invalidated when the source file is modified.

To disable caching:

```typescript
const proxyConfig = await igniter.getProxyConfig(undefined, false);
```

## Dependencies

This feature requires the following dependencies:

- `esbuild`: For TypeScript compilation
- `node:fs`: For file system operations
- `node:os`: For temporary directory access
- `node:url`: For module URL handling
