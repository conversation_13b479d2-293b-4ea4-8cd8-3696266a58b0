import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useState } from "react";

import { FullscreenExitOutlined, FullscreenOutlined } from "@ant-design/icons";
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PromptTemplate } from "./PromptTemplate";

const PromptCard: React.FC<{ data: PromptTemplate; actions?: React.ReactNode }> = (props) => {
  const { data, actions } = props;
  const runner = useCommandRunner();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 发送模板内容到聊天输入框
  const sendMessage = (message: string) => {
    runner(BuildInCommand.InsertTextIntoSender, {
      text: message,
    });
    runner(BuildInCommand.CloseSenderHeaderPanel);
  };

  // 显示预览图片
  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (data.preview) {
      setPreviewVisible(true);
    }
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewVisible(false);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const closePreview = () => {
    setPreviewVisible(false);
    setIsFullscreen(false);
  };

  return (
    <>
      <div
        className={`pts:group pts:transition-all ${data.deleted ? "pts:opacity-0" : "pts:opacity-100"}`}
        onClick={(e) => {
          e.stopPropagation();
          sendMessage(data.prompt);
        }}
      >
        <div className="pts:bg-white pts:p-3 pts:border pts:border-gray-200 pts:hover:border-blue-500 pts:rounded-sm pts:h-[84px] pts:cursor-pointer pts:select-none">
          <div className="pts:flex pts:items-center pts:mb-1 pts:font-medium">
            <span className="pts:bg-[rgba(108,144,242,0.10)] pts:px-[4px] pts:rounded-sm pts:text-[#6C90F2] pts:text-sm">
              <Icon icon="Template" />
            </span>
            <span className="pts:flex pts:flex-1 pts:justify-between pts:items-center pts:ml-2 pts:overflow-hidden pts:font-bold pts:text-black-85 pts:text-base pts:break-all pts:text-ellipsis pts:leading-[20px] pts:whitespace-nowrap">
              <Tooltip title={data.title}>
                <span className="pts:overflow-hidden pts:text-ellipsis">{data.title}</span>
              </Tooltip>
              {data.preview && data.type == "system" && (
                <Button size="small" type="text" onClick={handlePreview}>
                  <Icon icon="Eye" className="pts:text-primary" />
                </Button>
              )}
            </span>
          </div>
          <div className="pts:flex pts:m-0 pts:mt-3">
            <p className="pts:flex-1 pts:overflow-hidden pts:text-black-65 pts:text-xs pts:text-ellipsis pts:leading-relaxed pts:whitespace-nowrap">
              <Tooltip title={data.description}>{data.description}</Tooltip>
            </p>
            {actions}
          </div>
        </div>
      </div>

      {/* 预览图片模态框 */}
      <Modal
        open={previewVisible}
        onCancel={handleClosePreview}
        footer={null}
        width={isFullscreen ? "100vw" : "800px"}
        centered
        styles={{
          body: {
            padding: 0,
            height: isFullscreen ? "calc(100vh - 40px)" : "360px",
          },
        }}
        style={{
          maxWidth: "100vw",
        }}
        closable={false}
      >
        <div className="pts:flex pts:justify-between pts:items-center">
          <div className="pts:flex pts:items-center pts:font-bold pts:text-base">
            <Button
              type="text"
              onClick={closePreview}
              style={{
                padding: "0 6px",
              }}
            >
              <Icon icon="Left" className="pts:text-lg" />
            </Button>
            <div className="pts:pl-2">{data.title}</div>
          </div>
          <Button
            type="text"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
          />
        </div>
        <div
          className="pts:pt-4"
          style={{
            overflow: "auto",
            height: isFullscreen ? "calc(100vh - 80px)" : "320px",
          }}
        >
          {data.preview && (
            <img
              src={data.preview}
              alt="预览图片"
              style={{
                maxWidth: "100%",
                maxHeight: "80vh",
                objectFit: "contain",
              }}
            />
          )}
        </div>
      </Modal>
    </>
  );
};

export default PromptCard;
