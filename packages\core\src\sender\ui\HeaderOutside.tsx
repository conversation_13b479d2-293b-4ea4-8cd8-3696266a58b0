import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderHeaderOutside: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  const widgets = useMemo(() => {
    const widgets = agentConfig?.sender?.slots?.header?.widgets ?? [];
    return widgets.filter((i) => i.placement === "outside");
  }, [agentConfig]);

  return (
    <div className="ag:flex ag:flex-wrap ag:pb-2">
      {widgets.map((Widget, index) => (
        <div className="ag:mt-2 ag:mr-2" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderHeaderOutside;
