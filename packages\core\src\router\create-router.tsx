import React from "react";
import { RouteObject, createBrowserRouter, createMemoryRouter } from "react-router-dom";

import { getLoginUrl } from "@/core/common/vars";
import HostWrapper from "@/core/host/HostWrapper";
import { AgentChatConfig, ExtendRouterObject } from "@/types";

export interface RouterConfig {
  pages?: {
    login?: {
      enable: boolean;
      path?: string;
      Component?: React.FC;
    };
    home?: {
      path?: string;
      Component?: React.FC;
    };
    chat?: {
      path?: string;
      Component?: React.FC;
    };
    agentHome?: {
      path?: string;
      Component?: React.FC;
    };
    conversationHistory?: {
      path?: string;
      Component?: React.FC;
    };
  };
  routes?: ExtendRouterObject[];
  rootRoutes?: ExtendRouterObject[];
  authGuard?: () => boolean;
  loginUrl?: string;
  basename?: string;
  memory?: boolean;
}

export interface CreateRouterOptions {
  basename?: string;
}

export const createDefaultRouter: (
  config: RouterConfig,
  agentChatConfig: AgentChatConfig,
  loadConfig?: boolean,
) => ReturnType<typeof createMemoryRouter> = (config, agentChatConfig, loadConfig = true) => {
  const { pages = {}, routes = [], rootRoutes = [], authGuard, loginUrl, basename = "", memory } = config;
  const HomeComponent = pages.home?.Component;
  const loader = () => {
    if (config.authGuard) {
      const isAuthed = authGuard ? authGuard() : true;
      if (!isAuthed) {
        location.href = loginUrl ?? getLoginUrl();
      }
    }
    return null;
  };

  // auth 校验，添加默认 loader
  const $rootRoutes = rootRoutes.map((route) => {
    const { auth, ...restProps } = route;
    if (auth && !route.loader) {
      return {
        ...restProps,
        loader,
      };
    } else {
      return route;
    }
  });

  const $routes: RouteObject[] = [
    ...$rootRoutes,
    {
      path: "/",
      element: (
        <HostWrapper agentChatConfig={agentChatConfig} loadConfig={loadConfig}>
          {HomeComponent && <HomeComponent />}
        </HostWrapper>
      ),
      // 鉴权Guard
      loader,
      children: [
        {
          path: "chat/:id",
          Component: pages.chat?.Component,
        },
        {
          path: "agent/:code",
          Component: pages.agentHome?.Component,
        },
        {
          path: "conversations",
          Component: pages.conversationHistory?.Component,
        },
        ...routes,
      ],
    },
  ];

  if (pages.login?.enable) {
    const loginPageRoute: RouteObject = {
      path: "/login",
      Component: pages.login?.Component,
    };
    $routes.unshift(loginPageRoute);
  }

  const router = memory
    ? createMemoryRouter($routes, {
        basename,
        initialEntries: ["/"],
        initialIndex: 0,
      })
    : createBrowserRouter($routes, {
        basename,
      });

  return router;
};
