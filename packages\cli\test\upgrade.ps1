param($path="0.2.x")

$root = (Get-Location).Path

$projectPath = $root + "\test\projects\" + $path
Set-Location $projectPath
write-host (Get-Location).Path

$cliPath = $root + "\lib\cli.js"
node $cliPath upgrade

Write-Host "请选择一个操作："
Write-Host "1. 还原项目"
Write-Host "2. 直接退出"

# 读取用户输入
$choice = Read-Host "请输入您的选择"

if ($choice -eq "1") {
    Write-Host "还原项目..."
    git restore $projectPath
    git clean -fd $projectPath
} else {
    Write-Host "直接退出..."
}

Set-Location $root
