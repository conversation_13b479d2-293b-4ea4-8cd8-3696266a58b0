@layer theme, components, utilities;
@import "tailwindcss/theme.css" layer(theme) prefix(ag);
@import "tailwindcss/utilities.css" layer(utilities) prefix(ag);

@import "./theme/basic.css";
@import "./theme/highlight.css";
@import "./theme/markdown.css";
@import "katex/dist/katex.min.css";

/* bubble */
.cscs-agent-bubble .cscs-agent-bubble-footer-human {
  visibility: hidden;
}

.cscs-agent-bubble:hover .cscs-agent-bubble-footer-human {
  visibility: visible;
}

/** Sender */
.cscs-agent-sender-editor::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.cscs-agent-sender-editor::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.cscs-agent-sender-editor::-webkit-scrollbar-track {
  background-color: transparent;
}

.cscs-agent-sender-header-panel {
  box-shadow:
    0px 9px 28px 0px rgba(0, 0, 0, 0.08),
    0px 6px 16px -0px rgba(0, 0, 0, 0.08);
}

/** conversation */
.conversation-scrollable::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.conversation-scrollable::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.conversation-scrollable::-webkit-scrollbar-track {
  background-color: transparent;
}

.cscs-agent-sender {
  box-shadow:
    0px 4px 12px 0px rgba(7, 66, 151, 0.02),
    0px 2px 6px 0px rgba(7, 66, 151, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.09);
}
