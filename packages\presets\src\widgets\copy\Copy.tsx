import { Button, Tooltip, message as antdMessage } from "antd";
import React from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { MessageContext, MessageStatus } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const Copy: React.FC = () => {
  const context = React.useContext(MessageContext);
  const status = context?.message.status;

  if (status === MessageStatus.Cancelled) {
    return null;
  }

  return (
    <CopyToClipboard
      text={context?.message.getTextContent() ?? ""}
      onCopy={() => {
        antdMessage.success("复制成功");
      }}
    >
      <Tooltip title="复制">
        <Button
          type="text"
          size="small"
          icon={
            <span className="pts:text-black-65">
              <Icon icon="Copy" />
            </span>
          }
        />
      </Tooltip>
    </CopyToClipboard>
  );
};

export default Copy;
