import React, { PropsWithChildren } from "react";

import { AgentChatConfig, HostWrapper } from "@cscs-agent/core";

interface AgentEmbedProviderProps {
  agentChatConfig: AgentChatConfig;
  agentCode: string;
  loadConfig?: boolean;
}

const AgentEmbedProvider: React.FC<PropsWithChildren<AgentEmbedProviderProps>> = (props) => {
  const { agentChatConfig, agentCode, loadConfig, children } = props;

  return (
    <HostWrapper agentChatConfig={agentChatConfig} embedded={true} agentCode={agentCode} loadConfig={loadConfig}>
      {children}
    </HostWrapper>
  );
};

export default AgentEmbedProvider;
