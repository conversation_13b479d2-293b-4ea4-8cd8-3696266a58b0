import { Select, Tooltip } from "antd";
import React, { useEffect, useMemo } from "react";

import {
  BuildInCommand,
  get,
  useActiveConversationId,
  useCommandRunner,
  useConversationPreState,
  useConversationState,
} from "@cscs-agent/core";

interface DataSourceSelectProps {
  dataSourceApiUrl: string;
}

interface DataSourceResponse {
  name: string;
  db_type: string;
  host: string;
  port: number;
  username: string;
  database_name: string;
  id: number;
  created_at: string;
  updated_at: string;
}

const DataSourceSelect: React.FC<DataSourceSelectProps> = (props) => {
  const { dataSourceApiUrl } = props;
  const [options, setOptions] = React.useState<{ label: string; value: number }[]>([]);
  const runner = useCommandRunner();
  const [conversationId] = useActiveConversationId();
  const { state: preConversationState, setStateByKey: setPreStateByKey } = useConversationPreState();
  const { state: conversationState, setStateByKey } = useConversationState();

  const state = useMemo(() => {
    return conversationId ? conversationState : preConversationState;
  }, [conversationId, preConversationState, conversationState]);

  const dataSourceId = state?.$dataSourceId;

  useEffect(() => {
    if (dataSourceApiUrl) {
      get<DataSourceResponse[]>(dataSourceApiUrl).then((res) => {
        const $options = res.data.map((i) => ({
          label: i.name,
          value: i.id,
        }));
        setOptions($options);
      });
    }
  }, [dataSourceApiUrl]);

  useEffect(() => {
    if (dataSourceId === undefined && options.length > 0) {
      if (conversationId) {
        setStateByKey("$dataSourceId", options[0]?.value);
      } else {
        setPreStateByKey("$dataSourceId", options[0]?.value);
      }
    }
  }, [options, dataSourceId, conversationId]);

  useEffect(() => {
    if (dataSourceId !== undefined) {
      runner(BuildInCommand.UpdateExtendSenderParams, {
        setValue: (prevValue: any) => {
          prevValue.dataSourceId = dataSourceId;
          return prevValue;
        },
      });
    }
  }, [dataSourceId]);

  const handleChange = (value: string) => {
    if (conversationId) {
      setStateByKey("$dataSourceId", value);
    } else {
      setPreStateByKey("$dataSourceId", value);
    }
  };

  return (
    <Tooltip title="切换数据源">
      <Select
        className="ats:w-35"
        options={options}
        onChange={handleChange}
        value={dataSourceId}
        bordered={false}
        placeholder="请选择数据源"
      />
    </Tooltip>
  );
};

export default DataSourceSelect;
