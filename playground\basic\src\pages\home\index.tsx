import React, { useEffect } from "react";

import { Outlet, useMatch, useNavigate } from "@cscs-agent/core";
import { DefaultBasicLayout } from "@cscs-agent/presets";

const Home = () => {
  const matched = useMatch("/");
  const navigate = useNavigate();

  useEffect(() => {
    if (matched) {
      navigate("/agent/dynamic-page-creator", { replace: true });
    }
  }, [matched]);

  return (
    <div className="flex w-full h-screen app">
      <DefaultBasicLayout main={<Outlet />} />
    </div>
  );
};

export default Home;
