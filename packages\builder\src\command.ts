import { readFileSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

import { Command } from "commander";
import { vice } from "gradient-string";

import { Igniter } from "./igniter/index.js";
import { logo } from "./logo.js";

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJsonPath = join(__dirname, "..", "package.json");
const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

const program = new Command();

console.log(vice.multiline(logo));
// console.log(vice.multiline("✨ Powered by AI"));
console.log("");
// Configure main program
program
  .name("@cscs-agent/builder")
  .description("Builder tool for Agent Project")
  .version(packageJson.version, "-v, --version", "display version number");

program
  .command("start")
  .option("-p, --target-path ", "Target project path", process.cwd())
  .action(async (options) => {
    const igniter = new Igniter(options.targetPath);
    igniter.start();
  });

program
  .command("build")
  .option("-p, --target-path ", "Target project path", process.cwd())
  .action(async (options) => {
    const igniter = new Igniter(options.targetPath);
    igniter.build();
  });

// Global error handler
process.on("uncaughtException", (error) => {
  console.error(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

process.on("unhandledRejection", (reason) => {
  console.error(`Unhandled rejection: ${reason}`);
  process.exit(1);
});

program.parse();
