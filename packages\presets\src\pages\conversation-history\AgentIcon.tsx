import { getAssetUrl, useAgentConfigs } from "@cscs-agent/core";
import React from "react";

const AgentIcon: React.FC<{ code: string; className?: string }> = (props) => {
  const { code, className } = props;
  const agentConfigs = useAgentConfigs();
  const agentConfig = agentConfigs.find((i) => i.code === code);
  const logoUrl = agentConfig?.logo;
  const defaultLogoUrl = "/assets/common-agent-logo.png";

  return <img src={getAssetUrl(logoUrl ?? defaultLogoUrl)} className={className} width="24" />;
};

export default AgentIcon;
