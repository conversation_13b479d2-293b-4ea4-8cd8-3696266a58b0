import React, { forwardRef, useCallback, useEffect, useImperative<PERSON><PERSON>le, useMemo, useState } from "react";
import { Descendant, Editor as SlateEditor, Transforms, createEditor } from "slate";
import { withHistory } from "slate-history";
import { Editable, ReactEditor, Slate, withReact } from "slate-react";

import { useSenderContent } from "@/core";

import type {
  DatePickerElementProps,
  EditableTagElementProps,
  RangePickerElementProps,
  SelectElementProps,
  TagElementProps,
} from "./elements";
import {
  deserialize,
  useRenderElement,
  useSerializeToHTML,
  useSerializeToText,
  withCustomElements,
} from "./slate-plugins";

interface EditorProps {
  onChange: (value: string) => void;
  onEnterPress?: (event: KeyboardEvent) => void;
  ref?: React.Ref<EditorRef>;
}

export interface EditorRef {
  focus: () => void;
  clear: () => void;
  setText: (text: string) => void;
  insertText: (text: string) => void;
  getText: () => string;
}

// Define custom element types for Slate
declare module "slate" {
  interface CustomTypes {
    Editor: ReactEditor;
    Element: {
      type:
        | "paragraph"
        | "embedded-tag"
        | "embedded-editable-tag"
        | "embedded-select"
        | "embedded-datepicker"
        | "embedded-rangepicker";
      children: Descendant[];
      text?: string;
    } & EditableTagElementProps &
      TagElementProps &
      SelectElementProps &
      DatePickerElementProps &
      RangePickerElementProps;
    Text: {
      text: string;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    };
  }
}

const Editor = forwardRef<EditorRef, EditorProps>(function Editor(props, ref) {
  const { onChange, onEnterPress } = props;
  const [, setSenderContent] = useSenderContent();

  // Create Slate editor with plugins
  const editor = useMemo(() => withCustomElements(withHistory(withReact(createEditor()))), []);

  // Initial value for the editor
  const [value, setValue] = useState<Descendant[]>([
    {
      type: "paragraph",
      children: [{ text: "" }],
    },
  ]);

  const serializeToText = useSerializeToText();
  const serializeToHTML = useSerializeToHTML();

  const handleContentChange = useCallback(
    (text: string, rawValue: Descendant[]) => {
      onChange(text);
      const htmlValue = serializeToHTML(rawValue);
      setSenderContent({
        text: text,
        html: htmlValue,
        raw: rawValue,
      });
    },
    [onChange, serializeToHTML, setSenderContent],
  );

  // Handle value changes
  const handleChange = useCallback(
    (newValue: Descendant[]) => {
      setValue(newValue);
      const textContent = serializeToText(newValue);
      handleContentChange(textContent, newValue);
    },
    [handleContentChange, serializeToText],
  );

  // Call onChange on initialization
  useEffect(() => {
    const newTextValue = serializeToText(value);
    handleContentChange(newTextValue, value);
  }, [handleContentChange, serializeToText, value]);

  const renderElement = useRenderElement();

  // Render leaf nodes (text formatting)
  const renderLeaf = useCallback((props: any) => {
    let { children } = props;

    if (props.leaf.bold) {
      children = <strong>{children}</strong>;
    }

    if (props.leaf.italic) {
      children = <em>{children}</em>;
    }

    if (props.leaf.underline) {
      children = <u>{children}</u>;
    }

    return <span {...props.attributes}>{children}</span>;
  }, []);

  // Handle key events
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "Enter" && (event.ctrlKey || event.shiftKey)) {
        event.preventDefault();
        Transforms.insertText(editor, "\n");
        return;
      }
      if (event.key === "Enter" && onEnterPress) {
        onEnterPress(event.nativeEvent);
      }
    },
    [onEnterPress, editor],
  );

  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        ReactEditor.focus(editor);
      },
      clear: () => {
        Transforms.delete(editor, {
          at: {
            anchor: SlateEditor.start(editor, []),
            focus: SlateEditor.end(editor, []),
          },
        });
        Transforms.insertFragment(editor, [
          {
            type: "paragraph",
            children: [{ text: "" }],
          },
        ]);
        setValue([{ type: "paragraph", children: [{ text: "" }] }]);
      },
      setText: (text: string) => {
        Transforms.delete(editor, {
          at: {
            anchor: SlateEditor.start(editor, []),
            focus: SlateEditor.end(editor, []),
          },
        });
        const fragment = deserialize(new DOMParser().parseFromString(text, "text/html").body);
        Transforms.insertFragment(editor, fragment);

        const newTextValue = serializeToText(value);
        handleContentChange(newTextValue, value);
      },
      insertText: (text: string) => {
        ReactEditor.focus(editor);
        const fragment = deserialize(new DOMParser().parseFromString(text, "text/html").body);
        Transforms.insertFragment(editor, fragment);

        const newTextValue = serializeToText(value);
        handleContentChange(newTextValue, value);
      },
      getText: () => {
        return serializeToText(value);
      },
    }),
    [editor, value, serializeToText, handleContentChange],
  );

  return (
    <Slate editor={editor} initialValue={value} onValueChange={handleChange}>
      <Editable
        renderElement={renderElement}
        renderLeaf={renderLeaf}
        onKeyDown={handleKeyDown}
        style={{
          width: "100%",
          minHeight: "106px",
          maxHeight: "320px",
          fontSize: "14px",
          lineHeight: "210%",
          outline: "none",
          border: "none",
          wordBreak: "break-all",
          overflowY: "auto",
          color: "rgba(37, 45, 62, 0.85)",
        }}
        placeholder="发消息"
        className="cscs-agent-sender-editor"
      />
    </Slate>
  );
});

export default Editor;
