{"name": "@cscs-agent/embed", "version": "0.6.1", "description": "", "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"dev": "vite build --mode development --minify false", "watch": "vite build --mode development --minify false --watch", "build": "vite build", "lint": "eslint ./src --ext .ts,.tsx --fix"}, "peerDependencies": {"@ant-design/icons": ">=4.3.0", "@cscs-agent/core": "workspace:*", "@cscs-agent/icons": "workspace:*", "@cscs-agent/presets": "workspace:*", "antd": ">=4.24.15", "react": ">=16.12.0", "react-dom": ">=16.12.0"}, "devDependencies": {"@ant-design/icons": "^5.6.1", "@cscs-agent/core": "workspace:*", "@cscs-agent/icons": "workspace:*", "@cscs-agent/presets": "workspace:*", "@eslint/js": "^9.25.1", "@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/vite": "^4.1.4", "@types/react": "^16.9.34", "@types/react-dom": "^16.9.9", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-react": "^4.3.4", "antd": "^4.24.15", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "jsencrypt": "^3.3.2", "penpal": "^7.0.0", "react": ">=16.12.0", "react-dom": ">=16.12.0", "tailwindcss": "^4.1.4", "typescript": "^5.8.2", "vite": "^6.2.0", "vite-plugin-svgr": "^4.3.0"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0"}