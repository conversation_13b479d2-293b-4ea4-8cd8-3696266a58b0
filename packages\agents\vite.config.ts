import { resolve } from "path";

import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    typescript({
      tsconfig: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
    }),
    tailwindcss(),
    svgr({
      include: "**/*.svg?react",
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/presets",
      fileName: (format) => `index.${format}.js`,
      formats: ["es"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      external: [
        // Peer dependencies
        "@cscs-agent/core",
        "@cscs-agent/icons",
        "@cscs-agent/presets",
        "@ant-design/icons",
        "antd",
        "moment",
        "react",
        "react-dom",
      ],
      output: {
        assetFileNames: "agents-tailwind.css",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
}));
