import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { render, screen } from "@testing-library/react";

import Editor from "./Editor";

// Mock Slate modules
vi.mock("slate", () => ({
  createEditor: vi.fn(() => ({
    children: [],
    operations: [],
    selection: null,
    marks: null,
    isInline: vi.fn(),
    isVoid: vi.fn(),
    normalizeNode: vi.fn(),
    addMark: vi.fn(),
    apply: vi.fn(),
    deleteBackward: vi.fn(),
    deleteForward: vi.fn(),
    deleteFragment: vi.fn(),
    getFragment: vi.fn(),
    insertBreak: vi.fn(),
    insertSoftBreak: vi.fn(),
    insertFragment: vi.fn(),
    insertNode: vi.fn(),
    insertText: vi.fn(),
    removeMark: vi.fn(),
  })),
  Editor: {
    start: vi.fn(),
    end: vi.fn(),
  },
  Element: {
    isElement: vi.fn(),
  },
  Text: {
    isText: vi.fn(),
  },
  Transforms: {
    delete: vi.fn(),
    insertNodes: vi.fn(),
    insertText: vi.fn(),
    removeNodes: vi.fn(),
  },
}));

vi.mock("slate-react", () => ({
  Slate: ({ children }: any) => <div data-testid="slate-editor">{children}</div>,
  Editable: () => <div data-testid="editable">Editable Content</div>,
  withReact: vi.fn((editor) => editor),
  ReactEditor: {
    focus: vi.fn(),
  },
  useFocused: vi.fn(() => false),
  useSelected: vi.fn(() => false),
}));

vi.mock("slate-history", () => ({
  withHistory: vi.fn((editor) => editor),
}));

vi.mock("./elements", () => ({
  TagElement: ({ children }: any) => <span data-testid="tag-element">{children}</span>,
  EditableTagElement: ({ children }: any) => <span data-testid="editable-tag-element">{children}</span>,
  SelectElement: ({ children }: any) => <span data-testid="select-element">{children}</span>,
}));

vi.mock("./slate-plugins", () => ({
  withCustomElements: vi.fn((editor) => editor),
  useRenderElement: vi.fn(() => vi.fn()),
  useSerializeToText: vi.fn(() => vi.fn()),
  deserialize: vi.fn(() => []),
  serializeToHTML: vi.fn(() => ""),
  serializeToText: vi.fn(() => ""),
}));

describe("Editor Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders without crashing", () => {
    const mockOnChange = vi.fn();

    render(<Editor onChange={mockOnChange} />);

    expect(screen.getByTestId("slate-editor")).toBeTruthy();
    expect(screen.getByTestId("editable")).toBeTruthy();
  });

  it("renders with onEnterPress handler", () => {
    const mockOnChange = vi.fn();
    const mockOnEnterPress = vi.fn();

    render(<Editor onChange={mockOnChange} onEnterPress={mockOnEnterPress} />);

    expect(screen.getByTestId("slate-editor")).toBeTruthy();
    expect(screen.getByTestId("editable")).toBeTruthy();
  });

  it("calls onChange when content changes", () => {
    const mockOnChange = vi.fn();

    render(<Editor onChange={mockOnChange} />);

    // The onChange should be called during initialization with empty content
    expect(mockOnChange).toHaveBeenCalled();
  });

  it("works correctly when onEnterPress is not provided", () => {
    const mockOnChange = vi.fn();

    expect(() => {
      render(<Editor onChange={mockOnChange} />);
    }).not.toThrow();
  });
});
