import { defineMock } from "vite-plugin-mock-dev-server";

import { MessageMocker } from "@cscs-agent/mock";

export default defineMock([
  {
    url: "/api/chat/test",
    method: "POST",
    response: (req, res) => {
      const message = "《学而》是《论语》第一篇的篇名。《论语》中各篇一般都是以第一章的前二三个字作为该篇的篇名。《学而》一篇包括16章，内容涉及诸多方面。其中重点是「吾日三省吾身」；「节用而爱人，使民以时」；「礼之用，和为贵」以及仁、孝、信等道德范畴。";
      const mocker = new MessageMocker(req, res, 20);
      mocker.start(message);
    },
  },
]);
