import React from "react";

import { useActiveAgentConfig } from "@/core";
import { Role } from "@/types";

const Append: React.FC<{ role: Role }> = (props) => {
  const agentConfig = useActiveAgentConfig();
  const { role } = props;

  const widgets = agentConfig?.message?.slots?.append?.widgets ?? [];

  if (widgets.length === 0) return null;

  return (
    <div className="ag:pt-2">
      {widgets.map((Widget) => {
        if (!Widget.role || Widget.role === role) {
          return <Widget.component key={Widget.code} {...Widget.props} />;
        }
      })}
    </div>
  );
};

export default Append;
