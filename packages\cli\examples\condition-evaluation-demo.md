# LLM-Based Condition Evaluation Demo

This document demonstrates the new LLM-based condition evaluation feature for coreFile entries in the CLI upgrade command.

## Overview

The system now supports conditional file operations based on intelligent analysis of the codebase state. Instead of hardcoded logic, the system uses an LLM to evaluate natural language conditions.

## How It Works

### 1. CoreFile Configuration

```typescript
const coreFiles: CoreFileConfig[] = [
  {
    path: "src/widgets/button/index.tsx",
    prompt: "This is a template file",
    condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
  },
  // ... other files without conditions
];
```

### 2. Condition Evaluation Process

When the upgrade command encounters a file with a `condition` field:

1. **LLM Analysis**: The ConditionEvaluator uses @openai/agents to analyze the condition
2. **File System Access**: Automatically reads relevant files mentioned in the condition
3. **Intelligent Decision**: LLM evaluates whether the condition is satisfied
4. **Action**: Only processes the file if the condition is met

### 3. Example Scenarios

#### Scenario 1: No Widget Imports
**Condition**: "Add this file if `src/agent-config.tsx` doesn't import any widgets"

**agent-config.tsx content**:
```typescript
import type { AgentChatConfig } from "@cscs-agent/core";

export const config: AgentChatConfig = {
  agents: [{
    name: "Test Agent",
    message: {
      blocks: {
        widgets: []
      }
    }
  }]
};
```

**Result**: ✅ Condition satisfied - file will be added

#### Scenario 2: Widget Imports Present
**Condition**: "Add this file if `src/agent-config.tsx` doesn't import any widgets"

**agent-config.tsx content**:
```typescript
import type { AgentChatConfig } from "@cscs-agent/core";
import MyButton from "./widgets/button";
import Rating from "./widgets/rating";

export const config: AgentChatConfig = {
  agents: [{
    name: "Test Agent",
    message: {
      blocks: {
        widgets: [MyButton, Rating]
      }
    }
  }]
};
```

**Result**: ❌ Condition not satisfied - file will be skipped

## Configuration

### Environment Variables

```bash
# Required for LLM-based condition evaluation
OPENAI_API_KEY=your-api-key-here

# Optional: specify model (defaults to gpt-4o)
OPENAI_MODEL=gpt-4o
```

### Fallback Behavior

- **No API Key**: Defaults to satisfied (file will be processed)
- **LLM Error**: Defaults to satisfied with warning
- **Parse Error**: Attempts fallback parsing, defaults to satisfied

## Usage

The condition evaluation is automatically integrated into the existing upgrade workflow:

```bash
# Interactive mode
cscs-agent-cli upgrade --interactive

# Non-interactive mode
cscs-agent-cli upgrade --target-path ./my-project

# Dry run to see what would be processed
cscs-agent-cli upgrade --dry-run
```

## Logging Output

During execution, you'll see detailed logging:

```
ℹ Evaluating condition for src/widgets/button/index.tsx: Add this file if `src/agent-config.tsx` doesn't import any widgets
ℹ Condition evaluation result: satisfied
ℹ Reasoning: The agent-config.tsx file imports AgentChatConfig but no widget components
ℹ Creating new file /project/src/widgets/button/index.tsx - condition satisfied
```

## Benefits

1. **Intelligent Analysis**: Uses LLM understanding instead of regex patterns
2. **Flexible Conditions**: Natural language conditions are easy to write and understand
3. **Context Aware**: Analyzes multiple files and understands code relationships
4. **Robust Error Handling**: Graceful fallbacks ensure upgrade process continues
5. **Extensible**: Easy to add new conditional files with different conditions

## Technical Implementation

- **ConditionEvaluator**: Core class using @openai/agents for LLM integration
- **File System Access**: Uses existing FileSystemManager for reading files
- **Integration**: Seamlessly integrated into existing UpgradeCommand workflow
- **Type Safety**: Full TypeScript support with proper interfaces
- **Testing**: Comprehensive test coverage for all scenarios
