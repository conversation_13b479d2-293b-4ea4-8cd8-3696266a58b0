import { useAtom } from "jotai";
import { nanoid } from "nanoid";
import { useContext } from "react";

import { IMessagePackage, MessagePackageStatus, MessagePackageType } from "@/types";

import { createHumanMessage } from "../common/message";
import { AgentChatContext } from "./context";

/**
 * 消息列表Hook
 * @returns 消息列表状态和设置函数
 */
export const useMessages = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.messages);
};

/**
 * 当前激活智能体代码Hook
 * @returns 当前激活的智能体代码状态和设置函数
 */
export const useActiveAgentCode = () => {
  const store = useContext(AgentChatContext).store;
  return useAtom(store.activeAgentCode);
};

/**
 * 当前激活智能体菜单代码Hook
 * @returns 当前激活的智能体菜单代码状态和设置函数
 */
export const useActiveAgentMenuCode = () => {
  const store = useContext(AgentChatContext).store;
  return useAtom(store.activeAgentMenuCode);
};

/**
 * 人类消息创建Hook
 * 提供创建用户消息的功能
 * @returns 包含create方法的对象
 */
export const useHumanMessage = () => {
  const [, setMessages] = useMessages();
  const [activeAgentCode] = useActiveAgentCode();

  /**
   * 创建人类消息
   * @param content - 消息内容
   * @param extendData - 扩展数据
   * @returns 创建的消息对象，如果创建失败则返回null
   */
  const create = (content: string, extendData?: Record<string, any>, hidden = false) => {
    // 创建消息包
    const msgPackage: IMessagePackage = {
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: content,
    };

    // 创建消息
    if (!activeAgentCode) {
      console.error("activeAgentCode is null");
      return null;
    }
    const message = createHumanMessage({
      id: nanoid(),
      content: [msgPackage],
      agentCode: activeAgentCode,
      extendData,
      hidden,
    });
    if (!message) return null;
    setMessages((messages) => [...messages, message]);

    return message;
  };

  return { create };
};

/**
 * 当前激活会话Hook
 * @returns 当前激活的会话对象状态和设置函数
 */
export const useActiveConversation = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.activeConversation);
};

/**
 * 当前激活会话ID Hook
 * @returns 当前激活的会话ID状态和设置函数
 */
export const useActiveConversationId = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.activeConversationId);
};

/**
 * 会话列表Hook
 * @returns 会话列表状态和设置函数
 */
export const useConversations = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.conversations);
};

/**
 * 消息加载状态Hook
 * @returns AI消息加载状态和设置函数
 */
export const useIsLoadingMessage = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.isLoadingMessage);
};

/**
 * 发送器内容Hook
 * @returns 发送器内容状态和设置函数
 */
export const useSenderContent = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.senderContent);
};

/**
 * 当前会话状态Hook
 *
 * 提供对当前激活会话状态的管理功能，包括获取、设置和清除状态。
 * 状态数据按会话ID进行隔离存储。
 *
 * @returns 包含状态管理方法的对象
 */
export const useConversationState = <T = any>() => {
  const [conversationId] = useActiveConversationId();
  const store = useContext(AgentChatContext).store;
  const [state, setState] = useAtom(store.conversationState);

  /**
   * 获取当前会话的所有状态
   * @returns 当前会话的状态对象，如果没有会话ID则返回空对象
   */
  const getAllState = () => {
    return conversationId ? state[conversationId] : ({} as T);
  };

  /**
   * 根据键获取当前会话的特定状态值
   * @param key - 状态键名
   * @returns 对应的状态值
   */
  const getStateByKey = (key: string) => {
    const allState = getAllState();
    return allState[key] as T;
  };

  /**
   * 设置当前会话的特定状态值
   * @param key - 状态键名
   * @param value - 要设置的状态值
   */
  const setStateByKey = (key: string, value: any) => {
    setState((prevValues) => {
      if (!conversationId) return prevValues;

      const preValue = prevValues[conversationId];
      const newValue = {
        ...preValue,
        [key]: value,
      };
      return {
        ...prevValues,
        [conversationId]: newValue,
      };
    });
  };

  /**
   * 设置当前会话状态
   * @param value
   */
  const setAllState = (value: T | ((value: T) => T), $conversationId?: string) => {
    const id = $conversationId || conversationId;
    setState((prevValues) => {
      if (!id) return prevValues;

      return {
        ...prevValues,
        [id]: value,
      };
    });
  };

  /**
   * 清除当前会话的所有状态
   * 如果没有会话ID，会输出警告信息
   */
  const clearState = () => {
    if (conversationId) {
      setState((prevValues) => {
        const newValues = { ...prevValues };
        delete newValues[conversationId];
        return newValues;
      });
    } else {
      console.warn("Missing conversationId");
    }
  };

  return { state: getAllState(), getStateByKey, setStateByKey, setAllState, clearState };
};

/**
 * 会话前置状态Hook
 *
 * 提供对全局共享前置状态的管理功能。
 * 与会话状态不同，前置状态是全局共享的，不按会话ID隔离。
 * 通常用于存储发起会话前的临时状态。
 *
 * @returns 包含状态管理方法和完整状态对象的对象
 */
export const useConversationPreState = <T = any>() => {
  const store = useContext(AgentChatContext).store;
  const [state, setState] = useAtom(store.conversationPreState);

  /**
   * 根据键获取前置状态值
   * @param key - 状态键名
   * @returns 对应的状态值
   */
  const getStateByKey = (key: string) => {
    return state[key] as T;
  };

  /**
   * 设置前置状态值
   * @param key - 状态键名
   * @param value - 要设置的状态值
   */
  const setStateByKey = (key: string, value: any) => {
    setState((prevValues) => {
      return {
        ...prevValues,
        [key]: value,
      };
    });
  };

  /**
   * 清除所有前置状态
   * 将状态重置为空对象
   */
  const clearState = () => {
    setState({});
  };

  return { state, getStateByKey, setStateByKey, clearState };
};
