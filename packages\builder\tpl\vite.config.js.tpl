import path from "path";

import { defineConfig } from "vite";
import { mockDevServerPlugin } from "vite-plugin-mock-dev-server";

import { appDepInfoPlugin } from "@cscs-agent/builder";
import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  base: "/",
  plugins: [mockDevServerPlugin(), react(), typescript(), tailwindcss(), appDepInfoPlugin()], // Add TypeScript plugin
  server: {
    port: 3000,
    proxy: {
      "^/api": {
        target: "http://0.0.0.0:8000",
        changeOrigin: true,
      },
    },
  },
  // Add TypeScript configuration
  build: {
    sourcemap: false,
  },
  resolve: {
    extensions: [".js", ".ts", ".jsx", ".tsx", ".json"],
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@@": path.resolve(__dirname, "node_modules"),
    },
  },
  esbuild: {
    loader: "tsx",
    include: /\.(ts|tsx|jsx)$/,
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
});
