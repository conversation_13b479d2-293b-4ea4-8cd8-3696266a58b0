import React, { useEffect, useRef } from "react";

import { MessageRender } from "@cscs-agent/presets";

const SoloMessage: React.FC = () => {
  const renderRef = useRef<any>(null);

  const questOptions = {
    url: "/chat/test",
    body: {
      message: "markdown",
      agent_code: "default",
    },
    agentCode: "default",
  };

  useEffect(() => {
    renderRef.current?.runReq();
  }, []);

  return <MessageRender ref={renderRef} agentCode="default" requestOptions={questOptions} />;
};

export default SoloMessage;
