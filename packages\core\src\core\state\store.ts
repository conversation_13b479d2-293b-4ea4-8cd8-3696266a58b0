/**
 * @fileoverview Agent状态管理模块
 *
 * 该文件定义了基于Jotai的状态管理系统，用于管理智能体聊天应用的全局状态。
 * 包括消息管理、会话管理、智能体状态管理等核心功能。
 *
 * 主要功能：
 * - 全局登录状态管理
 * - 智能体状态存储和管理
 * - 消息和会话的状态管理
 * - 发送器内容状态管理
 * - 会话状态的持久化和管理
 */

import { atom, useAtom } from "jotai";
import { Descendant } from "slate";

import { IConversation } from "@/types";

import { Message } from "../common/message";

/**
 * 全局登录状态原子
 * 用于跟踪用户的登录状态
 */
export const loginState = atom(false);

/**
 * 登录状态Hook
 * @returns {[boolean, (value: boolean) => void]} 登录状态和设置函数的元组
 */
export const useLoginState = () => {
  return useAtom(loginState);
};

/**
 * 智能体状态存储类
 *
 * 使用Jotai原子来管理应用的各种状态，包括：
 * - 消息列表
 * - 会话列表
 * - 当前激活的智能体信息
 * - 加载状态
 * - 发送器内容
 * - 会话状态数据
 */
export class AgentStore {
  /** 消息列表原子 - 存储所有聊天消息 */
  public messages = atom<Message[]>([]);

  /** 会话列表原子 - 存储所有会话信息 */
  public conversations = atom<IConversation[]>([]);

  /** 当前激活的智能体代码 */
  public activeAgentCode = atom<string | null>(null);

  /** 当前激活的智能体菜单项代码 */
  public activeAgentMenuCode = atom<string | null>(null);

  /** 当前激活的会话对象 */
  public activeConversation = atom<IConversation | null>(null);

  /** 当前激活的会话ID */
  public activeConversationId = atom<string | null>(null);

  /** AI消息加载状态标识 */
  public isLoadingMessage = atom(false);

  /**
   * 发送器内容原子 - 存储用户输入的内容
   * 包含文本、HTML和Slate原始数据格式
   */
  public senderContent = atom<{
    text: string; // 纯文本内容
    html: string; // HTML格式内容
    raw: Descendant[]; // Slate编辑器原始数据
  }>({
    text: "",
    html: "",
    raw: [],
  });

  /** 会话状态数据 - 按会话ID存储的状态信息 */
  public conversationState = atom<Record<string, any>>({});

  /** 会话前置状态数据 - 全局共享的前置状态 */
  public conversationPreState = atom<Record<string, any>>({});
}
