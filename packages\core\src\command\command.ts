import { useUnmount } from "ahooks";
import Emitter from "eventemitter3";
import { useContext, useEffect, useState } from "react";

import { AgentChatContext } from "@/core/state/context";
import { CommandCallback, CommandEmitter, CommandUnsubscribe, IBaseCommandParams } from "@/types";

/**
 * 命令事件发射器
 * 用于发布和订阅命令事件
 */
export const commandEmitter: CommandEmitter = new Emitter();

function createCommandName(name: string, scope: string): string {
  return `@command/${scope}:${name}`;
}

/**
 * 订阅命令
 * 在React组件中订阅命令事件，组件卸载时自动取消订阅
 *
 * @param name 命令名称，只允许使用字母、数字和下划线
 * @param callback 命令回调函数
 * @returns 取消订阅函数
 */
export const useSubscribeCommand = (name: string, callback: CommandCallback): CommandUnsubscribe => {
  const { hostId } = useContext(AgentChatContext);
  const commandName = createCommandName(name, hostId);
  const [commandQueue, setCommandQueue] = useState<{ params: any }[]>([]);

  const unsubscribe = () => {
    commandEmitter.off(commandName, callback);
  };

  useEffect(() => {
    const handler = (params: any) => {
      const newCommand = {
        params: params,
      };
      setCommandQueue((prev) => {
        return [...prev, newCommand];
      });
    };

    commandEmitter.on(commandName, handler);
    return () => {
      commandEmitter.off(commandName, handler);
    };
  }, [commandName]);

  useEffect(() => {
    if (commandQueue.length === 0) return;
    const command = commandQueue.shift();
    callback(command?.params);
    setCommandQueue([...commandQueue]);
  }, [commandQueue, callback]);

  useUnmount(() => {
    setCommandQueue([]);
  });

  return unsubscribe;
};

/**
 * 运行命令
 * 触发指定名称的命令事件
 *
 */
export const useCommandRunner = () => {
  const { hostId } = useContext(AgentChatContext);

  const runner = (name: string, params?: IBaseCommandParams) => {
    // 延迟执行，确保所有订阅已经更新，命令在下一次渲染之前执行
    commandEmitter.emit(createCommandName(name, hostId), params);
  };

  return runner;
};
