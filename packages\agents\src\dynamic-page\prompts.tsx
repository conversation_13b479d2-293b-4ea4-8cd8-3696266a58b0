import { PromptConfig, createApiSelect, createEditableTag, createSelect } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";
import React from "react";

export const prompts: PromptConfig[] = [
  /**
    生成页面：生成一个页面，查询数据库中的【企业信息表】，在页面列表中展示【企业名称】、【企业性质】、【统一社会信用代码】
    1.输入框，placeholder：数据库表名 ， 默认值：企业信息表；
    2.输入框，placeholder：字段名 ，默认值：企业名称；
    3.输入框，placeholder：字段名 ， 默认值：企业性质；
    4.输入框，placeholder：字段名，  默认值：统一社会信用代码
   */
  {
    title: "SQL生成页面",
    description: "根据sql查询语句从零开始智能搭建页面",
    prompt: `根据以下sql查询语句生成动态页面：${createEditableTag("SQL语句", "select * from user_user_basic")}；`,
    icon: <Icon icon="CreatePage" />,
  },

  /**
    修改页面： 编辑已有动态页面：将【动态页面名称或编码】加载为当前配置对象
    1.下拉框，placeholder：动态页面名称或编码        下拉选项：所有动态页面名称-编码      字典：/api/page/system/dynamic/page/manage/list， 默认值：无
   */
  {
    title: "编辑页面",
    description: "对已存在的动态页面进行编辑",
    prompt: `编辑ID为 ${createApiSelect({
      placeholder: "请选择动态页面",
      apiConfig: {
        url: "/gateway/cluster/page/system/dynamic/page/manage/dropList",
        method: "GET",
        params: {},
        data: {},
        mapper: {
          label: "dynamicName",
          value: "id",
        },
      },
      style: {
        width: "200px",
      },
    })} 的动态页面；`,
    icon: <Icon icon="EditPage" />,
  },

  /**
    字段查询： 设置【字段名】作为该页面的查询条件，查询模式为【请选择查询模式】，下拉选项为【字典】
    1. 下拉框，placeholder：请选择查询模式  下拉选项：   精确查询，模糊查询，包含查询，范围查询。范围多选，下拉单选，下拉多选，树形单选，树形多选，级联单选，级联多选，单选框，多选框       默认值：无
    2.输入框，placeholder：字典，  默认值：无
   */
  {
    title: "字段查询",
    description: "根据字段查询列表",
    prompt: `设置${createEditableTag("字段名")}字段在查询条件中显示，查询模式为${createSelect(
      "请选择查询模式",
      [
        { label: "精确查询", value: "精确查询" },
        { label: "模糊查询", value: "模糊查询" },
        { label: "包含查询", value: "包含查询" },
        { label: "范围查询", value: "范围查询" },
        { label: "范围多选", value: "范围多选" },
        { label: "下拉单选", value: "下拉单选" },
        { label: "下拉多选", value: "下拉多选" },
        { label: "树形单选", value: "树形单选" },
        { label: "树形多选", value: "树形多选" },
        { label: "级联单选", value: "级联单选" },
        { label: "级联多选", value: "级联多选" },
        { label: "单选框", value: "单选框" },
        { label: "多选框", value: "多选框" },
      ],
      "",
      "选择查询模式",
    )}，下拉选项为${createEditableTag("字典")}；`,
  },

  /**
    字段合计：设置【字段名】为合计字段，合计值展示在表格上方
    1.输入框，placeholder：字段名 ，默认值：无
   */
  {
    title: "字段合计",
    description: "所有字段值的总和",
    prompt: `设置${createEditableTag("字段名")}为合计字段，合计值展示在表格上方；`,
  },

  /**
    树形列表：设置列表为树形，设置【parent_id】字段为id字段的父节点
    1.. 输入框， placeholder: 字段名，默认值：parent_id
   */
  {
    title: "树形列表",
    description: "根据parent_id和id字段拼接为树形列表",
    prompt: `设置表格显示样式为树形；`,
  },

  /**
    左右布局：设置【字段名】筛选项展示在左侧，当筛选值改变时【立即刷新】列表数据
    1..输入框，placeholder：字段名 ，默认值：无
    2.下拉框，placeholder：是否立即刷新，下拉选项： 立即刷新 ，不立即刷新    默认值：立即刷新
   */
  {
    title: "左右布局",
    description: "左侧展示选项，右侧展示列表",
    prompt: `设置${createEditableTag("字段名")}筛选项展示在左侧，当筛选值改变时${createSelect(
      "是否立即刷新",
      [
        { label: "立即刷新", value: "立即刷新" },
        { label: "不立即刷新", value: "不立即刷新" },
      ],
      "立即刷新",
      "选择刷新方式",
    )}列表数据；`,
  },

  /**
    导出按钮：允许导出当前数据，导出类型为【Excel】
    1.下拉框，placeholder：导出类型，选项：Excel， dbf，默认值：Excel
   */
  {
    title: "导出按钮",
    description: "配置导出按钮",
    prompt: `允许导出当前数据，导出类型为${createSelect(
      "导出类型",
      [
        { label: "Excel", value: "excel" },
        { label: "dbf", value: "dbf" },
      ],
      "Excel",
      "选择导出格式",
      "multiple",
    )}；`,
  },

  /**
    表头合并：设置【字段名】和【字段名】合并，合并标题为【标题名】
    1.输入框，placeholder：字段名，默认值：无
    2.输入框，placeholder：字段名，默认值：无
    3.输入框，placeholder：标题名，默认值：无
   */
  {
    title: "表头合并",
    description: "将多列表头合并并取名",
    prompt: `设置${createEditableTag("字段名")}和${createEditableTag("字段名")}表头合并，合并标题为${createEditableTag("标题名")}；`,
  },

  /**
    行合并：设置【字段名】数据合并展示，根据【字段名】进行合并，合并后展示【合并值】
    1. 输入框 ，placeholder：字段名，默认值：无
    2.输入框 ，placeholder：字段名，默认值：无
    3.下拉框，placeholder：合并值， 下拉选项：合计值，最大值，最小值，平均值  默认值：无
   */
  {
    title: "行合并",
    description: "合并展示相同值或计算值",
    prompt: `设置${createEditableTag("字段名")}数据合并展示，根据${createEditableTag("字段名")}进行合并，合并后展示${createSelect(
      "合并值",
      [
        { label: "合计值", value: "合计值" },
        { label: "最大值", value: "最大值" },
        { label: "最小值", value: "最小值" },
        { label: "平均值", value: "平均值" },
      ],
      "",
      "选择合并计算方式",
    )}；`,
  },
];
