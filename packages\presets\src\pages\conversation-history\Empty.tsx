import React from "react";

import { getAssetUrl } from "@cscs-agent/core";

const Empty: React.FC<{
  className?: string;
  style?: React.CSSProperties;
}> = (props) => {
  const { className, style } = props;
  return (
    <div className={className} style={style}>
      <img className="pts:block pts:mx-auto" src={getAssetUrl("/assets/empty.svg")} width="120" />
      <p className="pts:text-black-25 pts:text-center">暂无数据</p>
    </div>
  );
};

export default Empty;
