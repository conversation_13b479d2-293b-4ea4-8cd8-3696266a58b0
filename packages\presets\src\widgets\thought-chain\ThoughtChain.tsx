import React, { useEffect, useState } from "react";

import { CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { MarkdownRender } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface ThoughtChainItem {
  id: string;
  title: string;
  description: string;
  content?: string;
  status: "pending" | "success" | "error";
}

const ThoughtChain: React.FC<{ items: ThoughtChainItem[] }> = (props) => {
  const { items = [] } = props;
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    if (items.length > 0) {
      const lastNode = items[items.length - 1];
      if (lastNode.status === "pending") {
        setExpandedKeys((prev) => [...prev, lastNode.id]);
      }
    }
  }, [items.length]);

  const getStatusIcon = (status: Thought<PERSON>hainItem["status"]) => {
    switch (status) {
      case "success":
        return (
          <span className="pts:text-[#6C90F2]">
            <CheckCircleOutlined />
          </span>
        );
      case "pending":
        return <Icon icon="Loading" className="pts:text-[#6C90F2] pts:animate-spin" />;
      case "error":
        return (
          <span className="pts:text-[#6C90F2]">
            <CloseCircleOutlined />
          </span>
        );
    }
  };

  const getChevronIcon = (expanded: boolean) => {
    return expanded ? <Icon icon="Expanded" /> : <Icon icon="Right" />;
  };

  const toggleExpanded = (id: string) => {
    setExpandedKeys((prev) => {
      if (prev.includes(id)) {
        return prev.filter((key) => key !== id);
      }
      return [...prev, id];
    });
  };

  return (
    <div className="pts:mx-auto pts:max-w-4xl">
      <div
        className="pts:flex pts:items-center pts:gap-2 pts:mb-3 pts:text-primary pts:cursor-pointer"
        onClick={() => setCollapsed(!collapsed)}
      >
        <h1 className="pts:font-medium pts:text-sm">思维链</h1>
        <Icon icon={collapsed ? "Expanded" : "Collapsed"} />
      </div>

      <div
        className={`pts:bg-white pts:px-4 pts:border pts:border-[rgba(0,0,0,0.09)] pts:rounded-lg ${collapsed ? "pts:h-0 pts:opacity-0" : "pts:h-auto pts:pt-4 pts:opacity-100"} pts:transition-all pts:origin-top pts:duration-300 pts:overflow-hidden`}
      >
        {items.map((item, index) => (
          <div key={item.id} className="pts:border-gray-100">
            <div className="pts:focus:bg-gray-50 pts:focus:outline-none pts:w-full pts:text-left pts:transition-colors pts:duration-150">
              <div className="pts:flex pts:items-start pts:gap-3">
                <div className="pts:flex-shrink-0">{getStatusIcon(item.status)}</div>
                <div className="pts:flex-1 pts:pt-0.5 pts:cursor-pointer" onClick={() => toggleExpanded(item.id)}>
                  <div className="pts:flex pts:items-center pts:w-full">
                    <span className="pts:text-black-45 pts:text-sm">
                      {getChevronIcon(expandedKeys.includes(item.id))}
                    </span>
                    <h3 className="pts:pl-1 pts:font-bold pts:text-black-85 pts:text-sm">{item.title}</h3>
                  </div>
                </div>
              </div>
            </div>
            <div
              className={`pts:my-1 pts:ml-2 pts:pb-3 pts:pl-5 pts:border-[rgba(0,0,0,0.09)] pts:border-l ${index === items.length - 1 ? "pts:border-l-0" : ""}`}
            >
              <p className="pts:mt-1 pts:text-black-45 pts:text-xs">{item.description}</p>

              <div
                className={`${expandedKeys.includes(item.id) && item.content ? "pts:h-auto pts:opacity-100" : "pts:h-0 pts:opacity-0"} pts:transition-all pts:origin-top pts:duration-300 pts:overflow-hidden`}
              >
                <div className="pts:py-1">
                  <p className="pts:text-black-65 pts:text-sm pts:leading-relaxed">
                    <MarkdownRender content={item.content ?? ""} />
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ThoughtChain;
