import { createDeepSeek } from "@ai-sdk/deepseek";
import { setOpenAIAPI, setTracingDisabled } from "@openai/agents";
import { aisdk } from "@openai/agents-extensions";

const deepseek = createDeepSeek({
  baseURL: process.env.OPENAI_BASE_URL,
  apiKey: process.env.OPENAI_API_KEY ?? "",
});

export function createModel(model?: string) {
  return aisdk(deepseek(model ?? (process.env.OPENAI_MODEL as string)));
}

export function initModelConfig() {
  // set custom OpenAI client only if API key is provided
  if (process.env.OPENAI_API_KEY) {
    setOpenAIAPI("chat_completions");
  }
  setTracingDisabled(true);
}
