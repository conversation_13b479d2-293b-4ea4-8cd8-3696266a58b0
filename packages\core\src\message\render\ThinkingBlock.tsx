import { Collapse } from "antd";
import React from "react";

import { BlockStatus } from "@/types";
import { LoadingOutlined } from "@ant-design/icons";

import MarkdownRender from "./Markdown";

interface ThinkingBlockProps {
  content: string;
  status: BlockStatus;
}

const ThinkingBlock: React.FC<ThinkingBlockProps> = (props) => {
  const { content, status } = props;

  return (
    <div className="ag:mb-2">
      <Collapse
        defaultActiveKey={["1"]}
        style={{
          width: "100%",
        }}
      >
        <Collapse.Panel
          header={
            status === BlockStatus.Loading ? (
              <span>
                <LoadingOutlined />
                &nbsp;思考中...
              </span>
            ) : (
              "思考完成"
            )
          }
          key="1"
        >
          <MarkdownRender content={content} />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};

export default ThinkingBlock;
