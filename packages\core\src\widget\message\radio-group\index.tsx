import { Radio } from "antd";
import React, { useEffect, useState } from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveConversationId, useIsLatestMessage, useMessage } from "@/core";
import { createStateSet } from "@/message";
import { BuildInCommand, StateUpdateStrategy } from "@/types";

interface RadioGroupInteractiveWidgetProps {
  /** 选项 */
  options: { label: string; value: string }[];
  /** 发送后是否禁用 */
  disabledAfterSended?: boolean;
  /** 发送隐藏消息 */
  hideMessage?: boolean;
  /** 值 */
  value: any;
  /** 值路径 */
  valuePath: string;
}

const RadioGroupInteractiveWidget: React.FC<RadioGroupInteractiveWidgetProps> = (props) => {
  const { options, disabledAfterSended = true, hideMessage = true, value, valuePath } = props;
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();
  const [disabled, setDisabled] = useState(false);
  const isLatestMessage = useIsLatestMessage();
  const { message } = useMessage();

  const onChange = (value: string) => {
    const stateSetStr = createStateSet([
      {
        messageId: message?.id,
        path: valuePath,
        value,
        strategy: StateUpdateStrategy.Replace,
      },
    ]);
    const messageContent = `
${value}
${stateSetStr}
    `;

    runner(BuildInCommand.SendMessage, {
      message: messageContent,
      conversationId: activeConversationId,
      agentCode: activeAgentCode,
      hidden: hideMessage,
    });

    if (disabledAfterSended) {
      setDisabled(true);
    }
  };

  useEffect(() => {
    if (isLatestMessage) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [isLatestMessage]);

  return (
    <div className="ag:pt-2">
      <Radio.Group
        options={options}
        onChange={(e) => onChange?.(e.target.value)}
        value={value}
        disabled={disabled}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
        }}
      />
    </div>
  );
};

export default RadioGroupInteractiveWidget;
