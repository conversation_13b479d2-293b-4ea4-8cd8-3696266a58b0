import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useMemo } from "react";

import {
  BuildInCommand,
  MessageContext,
  MessageStatus,
  Role,
  useActiveAgentCode,
  useActiveConversationId,
  useCommandRunner,
  useMessages,
} from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const Regenerate: React.FC = () => {
  const context = React.useContext(MessageContext);
  const status = context?.message.status;
  const [messages] = useMessages();
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();

  const latestUserMessage = useMemo(() => {
    const useMessages = messages.filter((i) => i.role === Role.HUMAN);
    return useMessages.pop();
  }, [messages]);

  const reSend = () => {
    const message = latestUserMessage?.content.map((i) => i.data).join("");
    runner(BuildInCommand.SendMessage, {
      message: message,
      agentCode: activeAgentCode,
      conversationId: activeConversationId,
    });
  };

  if (status !== MessageStatus.Cancelled) {
    return null;
  }

  return (
    <Tooltip title="重新生成">
      <Button
        type="text"
        size="small"
        icon={
          <span className="pts:text-black-65">
            <Icon icon="Refresh" />
          </span>
        }
        onClick={reSend}
      />
    </Tooltip>
  );
};

export default Regenerate;
