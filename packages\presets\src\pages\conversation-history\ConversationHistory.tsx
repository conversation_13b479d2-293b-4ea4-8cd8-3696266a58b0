import { Button, Checkbox, Input, Modal, Select, Space, Spin, Typography, message } from "antd";
import moment from "moment";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { RedoOutlined } from "@ant-design/icons";
import { del, get, post, put, useAgentConfigs, useNavigate } from "@cscs-agent/core";
import type { ConversationData, ConversationHistoryResponse } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import AgentIcon from "./AgentIcon";
import Content from "./Content";
import Empty from "./Empty";
import NavigationBar from "./NavigationBar";
import Title from "./Title";

type ConversationItem = ConversationData & { deleted?: boolean; pending?: boolean };

const ConversationSearch: React.FC = () => {
  const [inputValue, setInputValue] = useState("");
  const [keyword, setKeyword] = useState("");
  const [conversations, setConversations] = useState<ConversationItem[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const agents = useAgentConfigs();
  const [agentCode, setAgentCode] = useState("");
  const [currentEditId, setCurrentEditId] = useState<string | null>(null);
  const size = 20;
  const [searchOnChange, setSearchOnChange] = useState(false);
  const [isBatchDelMode, setIsBatchDelMode] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);

  const agentOptions = useMemo(() => {
    const options = agents.map((i) => {
      return {
        label: i.name,
        value: i.code,
      };
    });
    options.unshift({
      label: "全部智能体",
      value: "",
    });
    return options;
  }, [agents]);

  useEffect(() => {
    handleSearch("", "", 1);
  }, []);

  useEffect(() => {
    if (conversations.length !== selectedIds.length) {
      setIsAllSelected(false);
    }
  }, [conversations, selectedIds]);

  const rename = useCallback(async (id: string, title: string) => {
    setCurrentEditId(null);

    put(`/conversation/${id}`, { title }).then(() => {
      setConversations((conversations) => {
        const conversation = conversations.find((i) => i.id === id);
        if (conversation) {
          conversation.title = title;
        }
        return [...conversations];
      });
    });
  }, []);

  const handleSearch = ($keyword: string, _agentCode: string, currentPage?: number) => {
    setKeyword($keyword);

    const nextPage = currentPage ? currentPage : page + 1;
    setPage(nextPage);
    if (nextPage === 1) {
      setConversations([]);
    }
    setLoading(true);
    get<ConversationHistoryResponse>(`/conversation/search`, {
      keyword: $keyword,
      agent_code: _agentCode,
      page: nextPage,
      size,
    })
      .then((res) => {
        setConversations((prev) => [...prev, ...res.data.data]);
        setHasMore(res.data.data.length === size);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleSelect = (item: ConversationItem) => {
    // if (currentEditId === item.id) return;
    navigate(`/chat/${item.id}`).then(() => {});
  };

  const remove = (id: string) => {
    if (loading) {
      message.warning("当前会话正在加载消息，请稍后再试");
      return;
    }

    setConversations((conversations) => {
      return conversations.map((item) => {
        if (item.id === id) {
          return { ...item, pending: true };
        }
        return item;
      });
    });

    del(`/conversation/${id}`).then(() => {
      setConversations((conversations) => {
        return conversations.map((item) => {
          if (item.id === id) {
            return { ...item, deleted: true };
          }
          return item;
        });
      });
    });
  };

  const batchRemove = () => {
    if (loading) {
      message.warning("当前会话正在加载消息，请稍后再试");
      return;
    }

    Modal.confirm({
      title: <span className="pts:text-black-85">确定要删除会话吗？</span>,
      content: <span className="pts:text-black-65">删除后，会话将不可恢复。</span>,
      onOk() {
        setConversations((conversations) => {
          return conversations.map((item) => {
            if (selectedIds.includes(item.id)) {
              return { ...item, pending: true };
            }
            return item;
          });
        });

        post(`/conversation/batch-delete`, { ids: selectedIds }).then(() => {
          // 重新加载列表
          handleSearch(keyword, agentCode, 1);
          setIsBatchDelMode(false);
          setSelectedIds([]);
        });
      },
      okButtonProps: {
        danger: true,
      },
    });
  };

  const selectAll = (all: boolean) => {
    if (all) {
      setSelectedIds(conversations.map((i) => i.id));
    } else {
      setSelectedIds([]);
    }
    setIsAllSelected(all);
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds((prev) => [...prev, id]);
    } else {
      setSelectedIds((prev) => prev.filter((i) => i !== id));
    }
  };

  const cancelSelect = () => {
    setIsBatchDelMode(false);
    setSelectedIds([]);
  };

  return (
    <div className="pts:bg-white pts:h-full">
      <NavigationBar />
      <div className="pts:flex pts:flex-col pts:h-[calc(100vh-48px)]">
        <div
          id="cscs-agent-conversations-result-scrollable"
          className="pts:flex-1 pts:pt-[52px] pts:overflow-y-auto mini-scrollbar"
        >
          <InfiniteScroll
            dataLength={conversations.length}
            next={() => handleSearch(keyword, agentCode)}
            hasMore={hasMore}
            loader={null}
            scrollableTarget="cscs-agent-conversations-result-scrollable"
            style={{ overflow: "hidden" }}
          >
            <div className="pts:flex pts:bg-white pts:mx-auto pts:border-[rgba(0,0,0,0.15)] pts:border-1 pts:rounded-sm pts:w-[752px] pts:h-10">
              <div className="pts:flex pts:items-center pts:border-[rgba(0,0,0,0.15)] pts:border-r-1 pts:min-w-[146px] pts:max-w-[146px]">
                <Select
                  placeholder="选择智能体"
                  className="pts:w-full"
                  options={agentOptions}
                  value={agentCode}
                  onChange={(value) => {
                    setAgentCode(value);
                    handleSearch(keyword, value, 1);
                  }}
                  bordered={false}
                />
              </div>
              <Input
                placeholder="请输入搜索关键字"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onPressEnter={() => {
                  if (!searchOnChange) {
                    setSearchOnChange(true);
                  }
                  handleSearch(inputValue, agentCode, 1);
                }}
                allowClear
                className="pts:text-black-85"
                bordered={false}
              />
            </div>

            <div className="pts:mx-auto pts:mt-[48px] pts:w-[800px]">
              <div className="pts:flex pts:flex-row-reverse pts:px-6 pts:pb-3" hidden={isBatchDelMode}>
                <Typography.Link onClick={() => setIsBatchDelMode(true)}>
                  <Space>
                    <Icon icon="Delete" />
                    <span>批量删除</span>
                  </Space>
                </Typography.Link>
              </div>
              {conversations.map((item) => (
                <div
                  key={item.id}
                  className={`pts:mb-2 pts:relative pts:transform-all pts:duration-300 ${item.deleted ? "pts:h-0 pts:opacity-0 pts:overflow-hidden" : "pts:h-auto pts:opacity-100"} ${item.pending ? "pts:animate-pulse" : ""}`}
                >
                  <div className="pts:top-[40px] pts:-left-7 pts:absolute" hidden={!isBatchDelMode}>
                    <Checkbox
                      checked={selectedIds.includes(item.id)}
                      onChange={(e) => handleCheckboxChange(item.id, e.target.checked)}
                    />
                  </div>
                  <div
                    className="pts:hover:bg-[#F8F9FB] pts:px-6 pts:py-3 pts:cursor-pointer"
                    onClick={() => handleSelect(item)}
                  >
                    <div className="pts:group pts:flex pts:justify-between pts:pb-2">
                      <div className="pts:flex pts:items-center pts:h-6 pts:font-medium pts:text-black-85 pts:text-md">
                        <AgentIcon code={item.current_agent_code} className="pts:mr-4" />
                        <Title
                          title={item.title}
                          keyword={keyword}
                          onEdited={(title) => {
                            rename(item.id, title);
                          }}
                          onCancel={() => {
                            setCurrentEditId(null);
                          }}
                          editable={currentEditId === item.id}
                        />
                        <div className="pts:hidden pts:group-hover:inline-block pts:ml-1">
                          <Button
                            type="text"
                            size="small"
                            icon={<Icon icon="Edit" className="pts:text-primary" />}
                            hidden={currentEditId === item.id}
                            onClick={(e) => {
                              e.stopPropagation();
                              setCurrentEditId(item.id);
                            }}
                          />
                        </div>
                        <div className="pts:hidden pts:group-hover:inline-block pts:ml-1">
                          <Button
                            type="text"
                            size="small"
                            icon={<Icon icon="Delete" className="pts:text-danger" />}
                            onClick={(e) => {
                              e.stopPropagation();
                              Modal.confirm({
                                title: <span className="pts:text-black-85">确定要删除会话吗？</span>,
                                content: <span className="pts:text-black-65">删除后，会话将不可恢复。</span>,
                                onOk() {
                                  remove(item.id);
                                },
                                okButtonProps: {
                                  danger: true,
                                },
                              });
                            }}
                          />
                        </div>
                      </div>
                      <div className="pts:text-black-65 pts:text-sm">
                        {moment(item.updated_at).format("YYYY-MM-DD")}
                      </div>
                    </div>
                    <div className="pts:h-[48px] pts:text-black-65">
                      <Content keyword={keyword} content={item.content}></Content>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </InfiniteScroll>

          {conversations.length === 0 && <Empty className="pts:mx-auto pts:mt-10" />}

          <div className="pts:text-center">
            <Spin spinning={loading} indicator={<RedoOutlined spin />} size="small" />
          </div>
        </div>
        <div
          className="pts:bg-white pts:pt-1 pts:overflow-hidden"
          hidden={!isBatchDelMode}
          style={{
            boxShadow: "1px -2px 4px 0px rgba(0,0,0,0.06)",
          }}
        >
          <div className="pts:flex pts:justify-between pts:items-center pts:mx-auto pts:w-[800px] pts:h-12">
            <Space className="pts:-ml-[31px]">
              <Checkbox checked={isAllSelected} onChange={(e) => selectAll(e.target.checked)} />
              <span>全选</span>
            </Space>

            <Space>
              <Button onClick={cancelSelect}>取消选择</Button>
              <Button danger onClick={batchRemove} disabled={selectedIds.length === 0}>
                批量删除
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <div
        className="pts:right-[40px] pts:bottom-[160px] pts:z-10 pts:fixed pts:flex pts:justify-center pts:items-center pts:bg-[rgba(37,45,62,0.45)] pts:hover:bg-[rgba(37,45,62,0.65)] pts:rounded-[20px] pts:w-10 pts:h-10 pts:active:animate-ping pts:cursor-pointer"
        onClick={() => {
          document.getElementById("cscs-agent-conversations-result-scrollable")?.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        }}
      >
        <Icon icon="BackTop" className="pts:text-white" />
      </div>
    </div>
  );
};

export default ConversationSearch;
