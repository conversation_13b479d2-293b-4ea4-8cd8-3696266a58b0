import fs from "fs";
import path from "path";

import type { Plugin } from "vite";

/**
 * Output method for dependency information
 */
export type OutputMethod = "console" | "global" | "both";

/**
 * Filter function to determine which dependencies to include
 */
export type DependencyFilter = (name: string, version: string, context: { isDevDependency: boolean }) => boolean;

/**
 * Package.json structure
 */
export interface PackageJson {
  name?: string;
  version?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  [key: string]: unknown;
}

/**
 * Dependency information structure
 */
export interface DependencyInfo {
  projectName: string;
  projectVersion: string;
  buildTime: string;
  isProduction: boolean;
  dependencies: Record<string, string>;
  totalDependencies: number;
}

/**
 * Plugin options interface
 */
export interface AppDepInfoPluginOptions {
  /** Whether to include devDependencies in the output (default: false for production builds) */
  includeDevDependencies?: boolean;
  /** Whether to include workspace dependencies (default: true) */
  includeWorkspaceDependencies?: boolean;
  /** Custom filter function to determine which dependencies to include */
  filter?: DependencyFilter | null;
  /** Output method: 'console' | 'global' | 'both' (default: 'console') */
  outputMethod?: OutputMethod;
  /** Global variable name when using 'global' or 'both' output methods */
  globalVariableName?: string;
  /** Whether to output on app startup (default: true) */
  outputOnStartup?: boolean;
  /** Custom package.json path (relative to project root) */
  packageJsonPath?: string;
}

/**
 * Runtime code generation options
 */
export interface RuntimeCodeOptions {
  outputMethod: OutputMethod;
  globalVariableName: string;
  outputOnStartup: boolean;
}

// 定义虚拟模块的名称
const VIRTUAL_MODULE_ID = "virtual:app-dep-info";
// 定义虚拟模块在文件系统中的解析路径（约定俗成，方便区分）
const RESOLVED_VIRTUAL_MODULE_ID = "\0" + VIRTUAL_MODULE_ID; // Rollup 约定以 \0 开头表示内部模块

/**
 * Vite plugin that collects dependency version information from package.json
 * and makes it available at runtime
 */
function appDepInfoPlugin(options: AppDepInfoPluginOptions = {}): Plugin {
  const {
    includeDevDependencies = false,
    includeWorkspaceDependencies = true,
    filter = null,
    outputMethod = "console",
    globalVariableName = "__DEPENDENCY_INFO__",
    outputOnStartup = true,
    packageJsonPath = "package.json",
  } = options;

  let dependencyInfo: DependencyInfo | null = null;
  let isProduction = false;

  return {
    name: "dependency-info",

    configResolved(config: { command: string }) {
      isProduction = config.command === "build";
    },

    buildStart() {
      try {
        // Read package.json from the project root
        const packageJsonFullPath = path.resolve(process.cwd(), packageJsonPath);

        if (!fs.existsSync(packageJsonFullPath)) {
          this.warn(`package.json not found at ${packageJsonFullPath}`);
          return;
        }

        const packageJsonContent = fs.readFileSync(packageJsonFullPath, "utf-8");
        const packageJson = JSON.parse(packageJsonContent) as PackageJson;

        // Collect dependency information
        const dependencies = packageJson.dependencies || {};
        const devDependencies = packageJson.devDependencies || {};

        // Determine which dependencies to include
        let allDependencies: Record<string, string> = { ...dependencies };

        // Include devDependencies based on options and build mode
        if (includeDevDependencies || (!isProduction && includeDevDependencies !== false)) {
          allDependencies = { ...allDependencies, ...devDependencies };
        }

        // Apply custom filter if provided
        if (typeof filter === "function") {
          const filteredDeps: Record<string, string> = {};
          Object.entries(allDependencies).forEach(([name, version]) => {
            if (filter(name, version, { isDevDependency: name in devDependencies })) {
              filteredDeps[name] = version;
            }
          });
          allDependencies = filteredDeps;
        }

        // Filter out workspace dependencies if not wanted
        if (!includeWorkspaceDependencies) {
          Object.keys(allDependencies).forEach((name) => {
            if (allDependencies[name].startsWith("workspace:")) {
              delete allDependencies[name];
            }
          });
        }

        dependencyInfo = {
          projectName: packageJson.name || "unknown",
          projectVersion: packageJson.version || "unknown",
          buildTime: new Date().toISOString(),
          isProduction,
          dependencies: allDependencies,
          totalDependencies: Object.keys(allDependencies).length,
        };

        this.info(`Collected ${dependencyInfo.totalDependencies} dependencies for runtime output`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.error(`Failed to read or parse package.json: ${errorMessage}`);
      }
    },

    resolveId(id) {
      if (id === VIRTUAL_MODULE_ID) {
        // 返回一个特殊的 ID，告诉 Rollup 这是我们的虚拟模块
        return RESOLVED_VIRTUAL_MODULE_ID;
      }
      return null; // 其他模块照常处理
    },

    // 当 Rollup 尝试加载 `RESOLVED_VIRTUAL_MODULE_ID` 时，我们提供其内容
    load(id) {
      if (id === RESOLVED_VIRTUAL_MODULE_ID) {
        if (!dependencyInfo) {
          this.warn("No dependency information available to inject");
          return;
        }

        // Generate the runtime code that will be injected
        const runtimeCode = generateRuntimeCode(dependencyInfo, {
          outputMethod,
          globalVariableName,
          outputOnStartup,
        });

        return runtimeCode;
      }
      return null; // 其他模块照常处理
    },
  };
}

/**
 * Generate runtime code for dependency information output
 */
function generateRuntimeCode(dependencyInfo: DependencyInfo, options: RuntimeCodeOptions): string {
  const { outputMethod, globalVariableName, outputOnStartup } = options;

  const dependencyInfoJson = JSON.stringify(dependencyInfo, null, 2);

  let code = `
// Dependency Information - Generated by vite-dependency-info-plugin
(function() {
  const dependencyInfo = ${dependencyInfoJson};
  
  function formatDependencyOutput(info) {
    const lines = [
      '🚀 App Information',
      '========================',
      \`Project: \${info.projectName} v\${info.projectVersion}\`,
      \`Build Time: \${info.buildTime}\`,
      \`Environment: \${info.isProduction ? 'Production' : 'Development'}\`,
      \`Total Dependencies: \${info.totalDependencies}\`,
      '',
      'Dependencies:'
    ];
    
    Object.entries(info.dependencies)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([name, version]) => {
        lines.push(\`  \${name}: \${version}\`);
      });
    
    return lines.join('\\n');
  }
`;

  if (outputMethod === "console" || outputMethod === "both") {
    code += `
  // Console output
  if (typeof console !== 'undefined' && console.log) {
    const output = formatDependencyOutput(dependencyInfo);
    console.log(output);
  }
`;
  }

  if (outputMethod === "global" || outputMethod === "both") {
    code += `
  // Global variable assignment
  if (typeof window !== 'undefined') {
    window.${globalVariableName} = dependencyInfo;
  } else if (typeof global !== 'undefined') {
    global.${globalVariableName} = dependencyInfo;
  }
`;
  }

  if (outputOnStartup) {
    code += `
  // Auto-execute on startup
  if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        // Code already executed above
      });
    }
  }
`;
  }

  code += `
})();
`;

  return code;
}

export default appDepInfoPlugin;
