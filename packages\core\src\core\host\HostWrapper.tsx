import React, { PropsWithChildren, useEffect, useState } from "react";

import { get } from "@/request";
import { AgentChatConfig, CustomNavigate, StandardResponse } from "@/types";

import AgentChatHost from "./AgentChatHost";

function loadAndMergeConfigs(config: AgentChatConfig) {
  return get<StandardResponse<any[]>>("/agent")
    .then((res) => {
      const agents = res.data.data;
      const localConfigs = config.agents;
      if (Array.isArray(agents)) {
        const newConfig = [];
        for (const i of agents) {
          const existed = localConfigs.find((c) => c.code === i.agent_code);
          if (existed) {
            newConfig.push({
              ...existed,
              name: i.agent_name,
              logo: i.icon,
              description: i.agent_desc,
              welcome: i.welcome_statement,
            });
          }
        }
        config.agents = newConfig;
      }
      return config;
    })
    .catch(() => {
      return config;
    });
}

// eslint-disable-next-line react-refresh/only-export-components
const HostWrapper: React.FC<
  PropsWithChildren<{
    agentChatConfig: AgentChatConfig;
    embedded?: boolean;
    navigate?: CustomNavigate;
    agentCode?: string;
    loadConfig?: boolean;
  }>
> = (props) => {
  const { agentChatConfig, embedded = false, navigate, agentCode, children, loadConfig = true } = props;
  const [config, setConfig] = useState<AgentChatConfig | null>(null);

  useEffect(() => {
    if (loadConfig) {
      loadAndMergeConfigs(agentChatConfig).then((result) => {
        setConfig(result);
      });
    } else {
      setConfig(agentChatConfig);
    }
  }, []);

  if (!children) {
    console.error("missing children");
    return null;
  }

  if (!config) {
    return null;
  }

  return (
    <AgentChatHost config={config} embedded={embedded} navigate={navigate} agentCode={agentCode}>
      {children}
    </AgentChatHost>
  );
};

export default HostWrapper;
