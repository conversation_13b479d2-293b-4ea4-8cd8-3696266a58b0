import {
  Outlet,
  RouterProvider,
  UNSAFE_LocationContext,
  UNSAFE_NavigationContext,
  UNSAFE_RouteContext,
  createBrowserRouter,
  useMatch,
  useNavigate as useNavigateOriginal,
  useParams,
} from "react-router-dom";

export * from "./create-router";
export * from "./navigate";

export {
  useParams,
  useNavigateOriginal,
  RouterProvider,
  createBrowserRouter,
  UNSAFE_LocationContext,
  UNSAFE_NavigationContext,
  UNSAFE_RouteContext,
  useMatch,
  Outlet,
};
