@layer theme, components, utilities;
@import "tailwindcss/theme.css" layer(theme) prefix(pts);
@import "tailwindcss/utilities.css" layer(utilities) prefix(pts);
@import "@cscs-agent/core/dist/theme/basic.css";

/** Sender */
.mini-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.mini-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.mini-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

.cscs-agent-home-container {
  background: url(/assets/<EMAIL>);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
}
