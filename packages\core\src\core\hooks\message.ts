import { useContext, useMemo } from "react";

import { MessageContext } from "@/message";
import { Role } from "@/types";

import { useMessages } from "../state/hooks";

export const useMessage = () => {
  const context = useContext(MessageContext);

  return {
    message: context?.message,
    state: context?.messageState,
  };
};

export const useIsLatestMessage = () => {
  const [messages] = useMessages();
  const { message } = useMessage();

  const isLatestMessage = useMemo(() => {
    const isAIMessage = message?.role === Role.AI;
    const tailMessage = messages[messages.length - 1];
    return isAIMessage && message?.id === tailMessage.id;
  }, [message, messages]);

  return isLatestMessage;
};
