import { DatePicker as AntdDatePicker, Tooltip } from "antd";
import moment, { Moment } from "moment";
import React, { useMemo, useState } from "react";
import { RenderElementProps } from "slate-react";

const { RangePicker: AntdRangePicker } = AntdDatePicker;

export interface RangePickerElementProps {
  placeholder?: [string, string];
  defaultValue?: [string, string];
  tooltips?: string;
  disabled?: boolean;
  format?: string;
  showTime?: boolean;
  allowClear?: boolean;
  separator?: string;
}

// RangePicker Element Component
export const RangePickerElement: React.FC<RenderElementProps> = ({ element }) => {
  const {
    placeholder = ["开始日期", "结束日期"],
    defaultValue,
    tooltips,
    disabled,
    format = "YYYY-MM-DD",
    showTime = false,
    allowClear = true,
    separator = "~",
  } = element as RangePickerElementProps;

  const value = useMemo(() => {
    if (Array.isArray(defaultValue)) {
      const start = defaultValue[0] ? moment(defaultValue[0].trim()) : null;
      const end = defaultValue[1] ? moment(defaultValue[1].trim()) : null;
      return [start, end] as any;
    } else {
      return null;
    }
  }, []);

  const [selectedRange, setSelectedRange] = useState<[Moment | null, Moment | null] | null>(value);

  const handleChange = (dates: [Moment | null, Moment | null] | null) => {
    setSelectedRange(dates);
    // Update the element's defaultValue to persist the selection
    if (dates && dates[0] && dates[1]) {
      (element as any).defaultValue = [dates[0].format(format), dates[1].format(format)];
    } else {
      (element as any).defaultValue = ["", ""];
    }
  };

  return (
    <span contentEditable={false} className="ag:px-1">
      <Tooltip title={tooltips}>
        <AntdRangePicker
          value={selectedRange}
          onChange={handleChange}
          disabled={disabled}
          placeholder={placeholder}
          format={format}
          showTime={showTime}
          allowClear={allowClear}
          separator={separator}
          size="small"
          className="ag:min-w-60"
        />
      </Tooltip>
    </span>
  );
};

export default RangePickerElement;
