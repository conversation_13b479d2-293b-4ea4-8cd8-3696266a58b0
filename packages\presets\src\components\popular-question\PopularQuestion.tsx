import { useThrottleFn } from "ahooks";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useEffect, useState } from "react";

import { SyncOutlined } from "@ant-design/icons";
import { BuildInCommand, StandardResponse, get, useActiveAgentCode, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface SuggestionConfig {
  prompt: string;
  heat: number;
}

const PopularQuestion: React.FC = () => {
  const [activeAgentCode] = useActiveAgentCode();
  const [questions, setQuestions] = React.useState<SuggestionConfig[]>([]);
  const runner = useCommandRunner();
  const [changeVisible, setChangeVisible] = useState(false);

  useEffect(() => {
    setQuestions([]);
    setChangeVisible(false);
    if (!activeAgentCode) return;
    handleRefresh();
  }, [activeAgentCode]);

  const handleRefresh = () => {
    if (!activeAgentCode) return;
    get<StandardResponse<SuggestionConfig[]>>(`/popular-question`, { agent_code: activeAgentCode }).then((res) => {
      if (Array.isArray(res.data.data)) {
        setQuestions(res.data.data);
        if (res.data.data.length === 4) {
          setChangeVisible(true);
        }
      } else {
        setQuestions([]);
      }
    });
  };

  const { run: handleClick } = useThrottleFn(
    (question: string) => {
      runner(BuildInCommand.SendMessage, { message: question, isNewConversation: true, agentCode: activeAgentCode });
    },
    {
      wait: 5000,
    },
  );

  return (
    <div className="pts:w-[800px]" hidden={questions.length === 0}>
      <div className="pts:flex pts:justify-between pts:mb-3">
        <div className="pts:flex pts:items-center">
          <div className="pts:flex pts:justify-center pts:items-center pts:bg-[rgba(108,144,242,0.10)] pts:rounded-lg pts:min-w-6 pts:min-h-6">
            <Icon icon="ChatFilled" className="pts:text-[#6C90F2] pts:text-base" />
          </div>
          <span className="pts:ml-2 pts:text-black-85 pts:text-sm p">猜你想问</span>
        </div>
        {changeVisible && (
          <Button
            type="text"
            size="small"
            icon={
              <span className="pts:text-black-45">
                <SyncOutlined />
              </span>
            }
            onClick={handleRefresh}
          >
            <span className="pts:pl-2 pts:text-black-45">换一换</span>
          </Button>
        )}
      </div>
      <div className="pts:flex pts:flex-wrap pts:-mx-2">
        {questions.map((question) => (
          <div className="pts:min-w-1/2 pts:max-w-1/2" key={question.prompt}>
            <Tooltip title={question.prompt}>
              <div
                onClick={() => handleClick(question.prompt)}
                className="pts:bg-white pts:mx-2 pts:mb-2 pts:px-4 pts:py-2 pts:border pts:border-[rgba(74,88,172,0.09)] pts:rounded-lg pts:overflow-hidden pts:text-black-65 pts:text-sm pts:text-ellipsis pts:whitespace-nowrap pts:hover:cursor-pointer pts:"
              >
                {question.prompt}
              </div>
            </Tooltip>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularQuestion;
