import { But<PERSON>, Toolt<PERSON>, message as antdMessage } from "antd";
import React from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { BuildInCommand, MessageContext, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const CopyToSender: React.FC = () => {
  const context = React.useContext(MessageContext);
  const runner = useCommandRunner();
  const text = context?.message.getTextContent() ?? "";

  return (
    <CopyToClipboard
      text={text}
      onCopy={() => {
        antdMessage.success("复制成功");
        runner(BuildInCommand.InsertTextIntoSender, {
          text,
        });
      }}
    >
      <Tooltip title="复制消息到发送框">
        <Button
          type="text"
          size="small"
          icon={
            <span className="pts:text-black-65">
              <Icon icon="CopyCreate" />
            </span>
          }
        />
      </Tooltip>
    </CopyToClipboard>
  );
};

export default CopyToSender;
