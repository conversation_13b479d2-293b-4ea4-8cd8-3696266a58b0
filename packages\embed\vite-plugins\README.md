# CSS Consolidator Vite Plugin

A custom Vite plugin that consolidates multiple CSS files into a single merged CSS file and copies it to the build output directory.

## Features

- ✅ Consolidates multiple CSS files into a single file
- ✅ Adds source comments to identify original files
- ✅ Customizable output filename
- ✅ Custom banner support
- ✅ Proper error handling for missing files
- ✅ Integrates seamlessly with Vite build process
- ✅ Handles CSS ordering and conflicts

## Usage

### Basic Usage

```typescript
import { cssConsolidator } from './vite-plugins/css-consolidator';

export default defineConfig({
  plugins: [
    cssConsolidator({
      sources: [
        'path/to/first.css',
        'path/to/second.css',
        'path/to/third.css'
      ],
      outputFileName: 'consolidated-styles.css'
    })
  ]
});
```

### Using the Pre-configured CSCS Agent Setup

```typescript
import { cssConsolidator, cscsAgentCssConfig } from './vite-plugins/css-consolidator';

export default defineConfig({
  plugins: [
    cssConsolidator(cscsAgentCssConfig)
  ]
});
```

## Configuration Options

### `CssConsolidatorOptions`

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `sources` | `string[]` | **Required** | Array of CSS file paths to consolidate |
| `outputFileName` | `string` | `'consolidated-styles.css'` | Output filename for the consolidated CSS |
| `addSourceComments` | `boolean` | `true` | Whether to add source comments to identify original files |
| `banner` | `string` | `'/* Consolidated CSS file generated by css-consolidator plugin */'` | Custom banner to add at the top of the consolidated file |

## Pre-configured Setup

The plugin includes a pre-configured setup for CSCS Agent CSS files:

```typescript
export const cscsAgentCssConfig: CssConsolidatorOptions = {
  sources: [
    "packages/core/dist/agent-tailwind.css",
    "packages/presets/dist/presets-tailwind.css", 
    "packages/icons/dist/icons.css",
  ],
  outputFileName: "cscs-agent-styles.css",
  addSourceComments: true,
  banner: "/* CSCS Agent - Consolidated Styles */\n/* This file contains all CSS dependencies for the CSCS Agent embed package */",
};
```

## Output Example

The consolidated CSS file will look like this:

```css
/* CSCS Agent - Consolidated Styles */
/* This file contains all CSS dependencies for the CSCS Agent embed package */

/* === Source: packages/core/dist/agent-tailwind.css === */
/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
/* ... CSS content from agent-tailwind.css ... */

/* === Source: packages/presets/dist/presets-tailwind.css === */
/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
/* ... CSS content from presets-tailwind.css ... */

/* === Source: packages/icons/dist/icons.css === */
.cscs-agent-icon{display:inline-flex;align-items:center;...}
/* ... CSS content from icons.css ... */
```

## Benefits

1. **Reduced HTTP Requests**: Instead of loading 3 separate CSS files, only 1 consolidated file is loaded
2. **Better Performance**: Fewer network requests improve page load times
3. **Easier Maintenance**: All CSS dependencies are consolidated in one place
4. **Source Tracking**: Source comments help identify which styles come from which original files
5. **Build Integration**: Seamlessly integrates with the Vite build process

## Migration from viteStaticCopy

This plugin replaces the previous `viteStaticCopy` configuration:

### Before (viteStaticCopy)
```typescript
viteStaticCopy({
  targets: [
    {
      src: "node_modules/@cscs-agent/core/dist/agent-tailwind.css",
      dest: "",
    },
    {
      src: "node_modules/@cscs-agent/presets/dist/presets-tailwind.css", 
      dest: "",
    },
    {
      src: "node_modules/@cscs-agent/icons/dist/icons.css",
      dest: "",
    },
  ],
})
```

### After (cssConsolidator)
```typescript
cssConsolidator(cscsAgentCssConfig)
```

## Error Handling

The plugin provides comprehensive error handling:

- **Missing Files**: Clear error messages when source CSS files are not found
- **Path Resolution**: Automatic path resolution relative to workspace root
- **Build Integration**: Errors are properly reported through Vite's error system

## File Size

The consolidated CSS file for CSCS Agent is approximately:
- **Uncompressed**: 1,522.50 kB
- **Gzipped**: 959.04 kB

This includes all Tailwind CSS utilities, component styles, and icon definitions needed for the embed package.
