/**
 * Tests for ConditionEvaluator
 */

import "dotenv/config";

import { join } from "path";

import { beforeEach, describe, expect, it, vi } from "vitest";

import { ConditionEvaluator } from "../condition-evaluator.js";
import { initModelConfig } from "../model.js";

initModelConfig();

// Mock @openai/agents
vi.mock("@openai/agents", async (importOriginal) => {
  const actual = await importOriginal<typeof import("@openai/agents")>();
  return {
    ...actual,
    Agent: vi.fn().mockImplementation(() => ({})),
    run: vi.fn(),
    MCPServerStdio: vi.fn().mockImplementation(() => ({
      connect: vi.fn(),
      close: vi.fn(),
    })),
  };
});

// Mock FileSystemManager
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    readFile: vi.fn(),
  })),
}));

// Mock fs functions
vi.mock("fs", () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
}));

describe("ConditionEvaluator", () => {
  let conditionEvaluator: ConditionEvaluator;
  let mockRun: any;
  let mockExistsSync: any;

  beforeEach(async () => {
    // Import the mocked modules
    const { run } = await import("@openai/agents");
    const { existsSync } = await import("fs");

    mockRun = run as any;
    const workspace = join(process.cwd(), "/test/projects/0.2.x");
    mockExistsSync = existsSync as any;
    conditionEvaluator = new ConditionEvaluator(workspace);
    await conditionEvaluator.init();

    vi.clearAllMocks();
  });

  describe("evaluateCondition", () => {
    it("should return satisfied=false when no OpenAI API key is available", async () => {
      // Remove API key
      conditionEvaluator = new ConditionEvaluator();
      await conditionEvaluator.init("");

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(false);
      expect(result.reasoning).toContain("LLM evaluation not available");
    });

    it("should evaluate condition using LLM when API key is available", async () => {
      // Set API key
      // Recreate evaluator with API key

      // Mock file existence and content
      mockExistsSync.mockReturnValue(true);

      // Mock LLM response
      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: true,
          reasoning: "The agent-config.tsx file does not import any widgets",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(true);
      expect(result.reasoning).toContain("does not import any widgets");
    });

    it("should handle LLM evaluation failure gracefully", async () => {
      // Mock LLM failure
      mockRun.mockRejectedValue(new Error("LLM API error"));

      const result = await conditionEvaluator.evaluateCondition("Add this file if condition is met");

      expect(result.satisfied).toBe(false); // Should default to satisfied on error
      expect(result.reasoning).toContain("Evaluation failed");
    });

    it("should parse malformed LLM response gracefully", async () => {
      // Mock malformed LLM response
      mockRun.mockResolvedValue({
        finalOutput: "This is not valid JSON response",
      });

      const result = await conditionEvaluator.evaluateCondition("Add this file if condition is met");

      expect(result.satisfied).toBe(true); // Should fallback to satisfied
      expect(result.reasoning).toContain("Failed to parse structured response");
    });

    it("should gather relevant files mentioned in condition", async () => {
      // Mock specific files exist
      mockExistsSync.mockImplementation((path: string) => {
        return path.includes("agent-config.tsx") || path.includes("main.tsx");
      });

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: false,
          reasoning: "Condition not met",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      await conditionEvaluator.evaluateCondition("Add this file if `src/agent-config.tsx` doesn't import any widgets");
    });
  });

  describe("integration scenarios", () => {
    it("should handle widget import detection scenario", async () => {
      // Mock agent-config.tsx with no widget imports
      mockExistsSync.mockReturnValue(true);

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: true,
          reasoning: "The agent-config.tsx file imports AgentChatConfig but no widget components",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(true);
      expect(result.reasoning).toContain("no widget components");
    });

    it("should handle widget import detection when widgets are present", async () => {
      // Mock agent-config.tsx with widget imports
      mockExistsSync.mockReturnValue(true);

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: false,
          reasoning: "The agent-config.tsx file already imports widget components: MyButton and Rating",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(false);
      expect(result.reasoning).toContain("already imports widget components");
    });
  });
});
