import { nanoid } from "nanoid";
import React, { useMemo } from "react";

import { AgentChatConfig, CustomNavigate } from "@/types";

import { AgentChatContext } from "../state/context";
import { AgentStore } from "../state/store";
import AgentCore from "./AgentCore";

export interface AgentChatHostProps {
  config: AgentChatConfig;
  embedded: boolean;
  navigate?: CustomNavigate;
  agentCode?: string;
}

const AgentChatHost: React.FC<AgentChatHostProps> = (props) => {
  const { config, embedded, navigate, agentCode, children } = props;
  const search = new URLSearchParams(window.location.search);
  const mode = search.get("mode") as any;
  const miniModeWidth = search.get("miniModeWidth");

  const contextValue = useMemo(() => {
    return {
      hostId: nanoid(),
      store: new AgentStore(),
      config,
      mode,
      miniModeWidth,
      embedded,
      navigate,
    };
  }, [config]);

  return (
    <AgentChatContext.Provider value={contextValue}>
      <AgentCore agentCode={agentCode}>{children}</AgentCore>
    </AgentChatContext.Provider>
  );
};

export default AgentChatHost;
