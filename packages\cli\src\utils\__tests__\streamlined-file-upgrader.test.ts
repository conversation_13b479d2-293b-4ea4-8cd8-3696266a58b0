/**
 * Tests for StreamlinedFileUpgrader
 */

import { beforeEach, describe, expect, it, vi } from "vitest";

import type { CompareFileItem } from "../../types.js";
import { StreamlinedFileUpgrader } from "../streamlined-file-upgrader.js";

// Mock the dependencies
vi.mock("../filesystem-manager.js");
vi.mock("../logger.js");
vi.mock("@openai/agents");

describe("StreamlinedFileUpgrader", () => {
  let upgrader: StreamlinedFileUpgrader;
  const mockTemplatePath = "/mock/template/path";

  beforeEach(() => {
    upgrader = new StreamlinedFileUpgrader(mockTemplatePath);
    vi.clearAllMocks();
  });

  describe("constructor", () => {
    it("should initialize with template path", () => {
      expect(upgrader).toBeDefined();
    });
  });

  describe("upgradeFiles", () => {
    it("should handle empty file list", async () => {
      const results = await upgrader.upgradeFiles([]);
      expect(results).toEqual([]);
    });

    it("should process multiple files", async () => {
      const mockFiles: CompareFileItem[] = [
        {
          filePath: "/project/package.json",
          content: '{"name": "test", "version": "1.0.0"}',
          prompt: "Test package.json file",
        },
        {
          filePath: "/project/src/main.tsx",
          content: "import React from 'react';",
          prompt: "Test main.tsx file",
        },
      ];

      // Mock the file system manager
      const mockFsManager = {
        fileExists: vi.fn().mockReturnValue(false),
        readFile: vi.fn(),
        writeFile: vi.fn(),
        backupFile: vi.fn(),
      };

      // Replace the private fsManager with our mock
      (upgrader as any).fsManager = mockFsManager;

      const results = await upgrader.upgradeFiles(mockFiles);

      // Since template files don't exist (mocked as false), results should be empty
      expect(results.length).toEqual(2);
    });
  });

  describe("generateDiff", () => {
    it("should generate diff correctly", () => {
      const originalContent = `line 1
line 2
line 3`;
      const modifiedContent = `line 1
modified line 2
line 3
new line 4`;

      const diffResult = (upgrader as any).generateDiff("/test/file.ts", originalContent, modifiedContent);

      expect(diffResult.filePath).toBe("/test/file.ts");
      expect(diffResult.hasChanges).toBe(true);
      expect(diffResult.diffLines.length).toBeGreaterThan(0);
      expect(diffResult.originalContent).toBe(originalContent);
      expect(diffResult.modifiedContent).toBe(modifiedContent);
    });

    it("should detect no changes when content is identical", () => {
      const content = `line 1
line 2
line 3`;

      const diffResult = (upgrader as any).generateDiff("/test/file.ts", content, content);

      expect(diffResult.filePath).toBe("/test/file.ts");
      expect(diffResult.hasChanges).toBe(false);
      expect(diffResult.originalContent).toBe(content);
      expect(diffResult.modifiedContent).toBe(content);
    });
  });

  describe("resolveTemplatePath", () => {
    it("should resolve package.json path correctly", () => {
      const result = (upgrader as any).resolveTemplatePath("/project/package.json");
      expect(result).toContain("package.json");
    });

    it("should resolve src file paths correctly", () => {
      const result = (upgrader as any).resolveTemplatePath("/project/src/main.tsx");
      expect(result).toContain("main.tsx");
      expect(result).toMatch(/src[/\\]main\.tsx$/);
    });

    it("should resolve root config files correctly", () => {
      const result = (upgrader as any).resolveTemplatePath("/project/tsconfig.json");
      expect(result).toContain("tsconfig.json");
    });
  });

  describe("getLanguageFromExtension", () => {
    it("should return correct language for TypeScript files", () => {
      const result = (upgrader as any).getLanguageFromExtension("ts");
      expect(result).toBe("typescript");
    });

    it("should return correct language for TSX files", () => {
      const result = (upgrader as any).getLanguageFromExtension("tsx");
      expect(result).toBe("typescript");
    });

    it("should return correct language for JSON files", () => {
      const result = (upgrader as any).getLanguageFromExtension("json");
      expect(result).toBe("json");
    });

    it("should return empty string for unknown extensions", () => {
      const result = (upgrader as any).getLanguageFromExtension("unknown");
      expect(result).toBe("");
    });
  });

  describe("determineUpdatePriority", () => {
    it("should return critical for package.json changes", () => {
      const originalContent = '{"dependencies": {"react": "^17.0.0"}}';
      const modifiedContent = '{"dependencies": {"react": "^18.0.0"}}';

      const result = (upgrader as any).determineUpdatePriority(originalContent, modifiedContent);
      expect(result).toBe("critical");
    });

    it("should return recommended for import changes", () => {
      const originalContent = "const React = require('react');";
      const modifiedContent = "import React from 'react';";

      const result = (upgrader as any).determineUpdatePriority(originalContent, modifiedContent);
      expect(result).toBe("recommended");
    });

    it("should return optional for minor changes", () => {
      const originalContent = "const x = 1;";
      const modifiedContent = "const x = 2;";

      const result = (upgrader as any).determineUpdatePriority(originalContent, modifiedContent);
      expect(result).toBe("optional");
    });
  });

  describe("displayDiff", () => {
    it("should display diff without throwing errors", () => {
      const mockDiffResult = {
        filePath: "/test/file.ts",
        originalContent: "original",
        modifiedContent: "modified",
        diffLines: [
          { type: "removed" as const, content: "original", lineNumber: 1 },
          { type: "added" as const, content: "modified", lineNumber: 1 },
        ],
        hasChanges: true,
      };

      // Mock console.log to capture output
      const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

      upgrader.displayDiff(mockDiffResult);

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
