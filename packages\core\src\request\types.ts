/**
 * Request module types
 *
 * 定义请求模块的类型和接口
 */

import { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from "axios";

/**
 * 请求配置接口
 * 扩展Axios请求配置，添加自定义属性
 */
export interface RequestOptions extends AxiosRequestConfig {
  /** 是否显示错误信息 */
  showError?: boolean;
  /** 是否使用取消令牌 @deprecated 使用 signal 代替 */
  useCancelToken?: boolean;
  /** 自定义请求标识符 */
  requestId?: string;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟(ms) */
  retryDelay?: number;
  /** AbortSignal 用于取消请求 */
  signal?: AbortSignal;
}

/**
 * 请求实例配置接口
 * 定义创建请求实例时的配置选项
 */
export interface RequestConfig {
  /** 基础URL前缀 */
  baseURL?: string;
  /** 默认超时时间(ms) */
  timeout?: number;
  /** 默认请求头 */
  headers?: Record<string, string>;
  /** 请求拦截器 */
  requestInterceptors?: RequestInterceptor[];
  /** 响应拦截器 */
  responseInterceptors?: ResponseInterceptor[];
  /** 错误处理函数 */
  errorHandler?: (error: AxiosError) => Promise<never>;
}

/**
 * 请求拦截器接口
 * 定义请求拦截器的结构
 */
export interface RequestInterceptor {
  /** 拦截器名称 */
  name: string;
  /** 拦截器处理函数 */
  onFulfilled: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
  /** 拦截器错误处理函数 */
  onRejected?: (error: unknown) => unknown;
}

/**
 * 响应拦截器接口
 * 定义响应拦截器的结构
 */
export interface ResponseInterceptor {
  /** 拦截器名称 */
  name: string;
  /** 拦截器处理函数 */
  onFulfilled: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
  /** 拦截器错误处理函数 */
  onRejected?: (error: unknown) => unknown;
}

/**
 * 请求取消接口
 * 定义请求取消的方法
 */
export interface RequestCanceler {
  /** 取消请求的方法 */
  cancel: (message?: string) => void;
}

/** defaultErrorHandler 配置项 */
export interface DefaultErrorHandlerOption {
  /** 是否跳转到登录页 */
  redirectToLogin?: boolean;
  /** 登录页地址 */
  loginUrl?: string;
  /** 状态码处理逻辑 */
  errorHandles?: Record<number, () => void>;
}

/** 默认请求拦截器配置项 */
export interface DefaultRequestInterceptorOptions {
  /** 是否添加时间戳 */
  addTimestamp: boolean;
  /** 是否添加Token */
  addToken: boolean;
  /** 是否添加前缀 */
  addPrefix: boolean;
  /** 前缀 */
  prefix: string;
}
