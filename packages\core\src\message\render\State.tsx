import { getProperty, setProperty } from "dot-prop";
import React, { useContext, useEffect, useState } from "react";

import { useCommandRunner } from "@/command";
import { BuildInCommand, IUpdateMessageStateParams, StateSet, StateUpdateStrategy } from "@/types";

import MessageContext from "../ui/Context";

interface StateProps {
  setList: StateSet[];
  scope?: string;
}

function updateValue(prevValues: any, path: string, value: any, strategy: StateUpdateStrategy) {
  const newPrevValues = getProperty(prevValues, path);

  if (typeof newPrevValues === "string" && strategy === StateUpdateStrategy.IncrementalMerge) {
    let currentValue = "";
    if (typeof currentValue === "number") {
      currentValue = String(currentValue);
    }
    if (typeof currentValue === "string") {
      currentValue = value as string;
    }
    const newValue = newPrevValues + currentValue;
    setProperty(prevValues, path, newValue);
  } else {
    setProperty(prevValues, path, value);
  }
}

const useStateUpdateQueue = () => {
  const [queue, setQueue] = useState<StateSet[]>([]);
  const runner = useCommandRunner();

  const push = (...items: StateSet[]) => {
    setQueue((prev) => [...prev, ...items]);
  };

  useEffect(() => {
    if (queue.length > 0) {
      const currentSet = queue.pop();
      if (!currentSet) return;
      // scope 格式为 @message/messageId
      const messageId = currentSet?.scope?.match(/@message\/(.*)/)?.[1];
      runner(BuildInCommand.UpdateMessageState, {
        messageId,
        setValue: (values) => {
          const $newValues = structuredClone(values);
          updateValue($newValues, currentSet.path, currentSet.value, currentSet.strategy);
          return $newValues;
        },
      } as IUpdateMessageStateParams);
      setQueue([...queue]);
    }
  }, [queue]);

  return { push };
};

const State: React.FC<StateProps> = (props) => {
  const { setList } = props;
  const messageContext = useContext(MessageContext);
  const setMessageState = messageContext?.setMessageState;
  const { push } = useStateUpdateQueue();

  useEffect(() => {
    if (!setMessageState) return;

    const scopeSets: StateSet[] = [];
    setMessageState((prevValues) => {
      const newValues = structuredClone(prevValues);
      for (const set of setList) {
        const scope = set.scope;
        if (scope) {
          scopeSets.push(set);
        } else {
          updateValue(newValues, set.path, set.value, set.strategy);
        }
      }
      push(...scopeSets);
      return newValues;
    });
  }, [setList]);

  return null;
};

export default State;
