import type { AgentChatConfig } from "@cscs-agent/core";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "问答助手",
      code: "dynamic-page-qa",
      logo: "/assets/dynamic-page-qa-logo.png",
      welcome: "Hi，欢迎使用问答助手",
      description: "面向动态页面管理场景，提供自然语言交互式",
      message: {
        blocks: {
          widgets: [],
        },
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      prompts: [
        {
          title: "只是一个提示词",
          content: "请查询{{[企业名称]}}获取企业信息",
        },
      ],
      commands: [],
      suggestions: [],
      sender: {
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "get",
        },
      },
    },
  ],
};
