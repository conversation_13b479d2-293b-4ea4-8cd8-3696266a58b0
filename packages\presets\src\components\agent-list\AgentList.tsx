import React, { useContext } from "react";

import { useNavigate } from "@cscs-agent/core";
import {
  AgentChatContext,
  getAssetUrl,
  useActiveAgentCode,
  useActiveAgentMenuCode,
  useMessages,
} from "@cscs-agent/core";

// Agent module exports
const AgentList: React.FC = () => {
  const { config } = useContext(AgentChatContext);
  const agents = config.agents ?? [];
  const [, setMessages] = useMessages();
  const navigate = useNavigate();
  const defaultLogoUrl = "/assets/common-agent-logo.png";

  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();

  const handleAgentClick = (code: string) => {
    if (activeAgentMenuCode === code) return;
    navigate(`/agent/${code}`).then(() => {
      setActiveAgentMenuCode(code);
      setActiveAgentCode(code);
      setMessages([]);
    });
  };

  return (
    <ul className="pts:pt-2 pts:pl-0 pts:h-full pts:overflow-x-hidden pts:overflow-y-auto">
      {agents.map((i) => {
        return (
          <li
            key={i.code}
            className={`pts:flex pts:items-center pts:cursor-pointer pts:py-2 pts:px-4 hover:bg-[rgba(0,0,0,0.05)] ${
              activeAgentMenuCode === i.code ? "pts:bg-[rgba(237,238,239,1)]" : ""
            }`}
            onClick={() => handleAgentClick(i.code)}
          >
            <i className="pts:mr-2">
              <img src={getAssetUrl(i.logo ?? defaultLogoUrl)} width="16" />
            </i>
            <span className="pts:text-black-85 pts:text-sm">{i.name}</span>
          </li>
        );
      })}
    </ul>
  );
};

export default AgentList;
