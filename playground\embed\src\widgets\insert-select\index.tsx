import { Button } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const InsertSelect = () => {
  const runner = useCommandRunner();

  return (
    <Button
      onClick={() =>
        runner(BuildInCommand.InsertSelectIntoSender, {
          placeholder: "请选择企业类型",
          options: [
            { label: "有限责任公司", value: "limited_company" },
            { label: "股份有限公司", value: "joint_stock_company" },
            { label: "个人独资企业", value: "sole_proprietorship" },
            { label: "合伙企业", value: "partnership" },
            { label: "外商投资企业", value: "foreign_investment" },
          ],
          defaultValue: "",
          tooltips: "选择企业的法律组织形式",
          disabled: false,
        })
      }
      size="small"
      shape="round"
    >
      插入选择器
    </Button>
  );
};

export default InsertSelect;
