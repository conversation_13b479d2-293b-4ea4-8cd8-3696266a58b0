import { defineMock } from "vite-plugin-mock-dev-server";

import { MessageMocker } from "@cscs-agent/mock";

export default defineMock([
  {
    url: "x/api/chat/test",
    method: "POST",
    response: (req, res) => {
      const message = `
        工具调用
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool1Status</path>
          <value>loading</value>
        </set>
      </state>

      <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具1输出结果xxxxx</result>
            <status>{{state.tool1Status}}</status>
        </props>
      </widget>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool1Status</path>
          <value>success</value>
        </set>
      </state>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool2Status</path>
          <value>loading</value>
        </set>
      </state>
      <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>查询工具调用</name>
            <result>工具2输出结果xxxxx</result>
            <status>{{state.tool2Status}}</status>
        </props>
      </widget>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool2Status</path>
          <value>success</value>
        </set>
      </state>
    </message-embedded>
    11111111111111111111111
      `;
      const mocker = new MessageMocker(req, res, 10);
      mocker.start(message);
    },
  },
]);
