import React from "react";
import { useParams } from "react-router-dom";

import { AgentEmbedProvider } from "@cscs-agent/embed";
import { ChatEmbedded } from "@cscs-agent/presets";

import { config } from "../../agent-config";

const AgentPage: React.FC = () => {
  const { code } = useParams();

  if (!code) return null;

  return (
    <div className="h-[600px]">
      <AgentEmbedProvider agentChatConfig={config} agentCode={code} loadConfig={false}>
        <ChatEmbedded />
      </AgentEmbedProvider>
    </div>
  );
};

export default AgentPage;
