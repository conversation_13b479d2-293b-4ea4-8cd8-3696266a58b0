import { existsSync, readFileSync } from "fs";
import { resolve } from "path";

import { Plugin } from "vite";

interface CssConsolidatorOptions {
  /**
   * Array of CSS file paths to consolidate
   */
  sources: string[];

  /**
   * Output filename for the consolidated CSS
   */
  outputFileName?: string;

  /**
   * Whether to add source comments to identify original files
   */
  addSourceComments?: boolean;

  /**
   * Custom banner to add at the top of the consolidated file
   */
  banner?: string;
}

/**
 * Vite plugin that consolidates multiple CSS files into a single merged CSS file
 * and copies it to the build output directory.
 */
export function cssConsolidator(options: CssConsolidatorOptions): Plugin {
  const {
    sources,
    outputFileName = "consolidated-styles.css",
    addSourceComments = true,
    banner = "/* Consolidated CSS file generated by css-consolidator plugin */",
  } = options;

  return {
    name: "css-consolidator",

    // Run during the build process
    generateBundle() {
      try {
        // Find the workspace root by going up two levels from the embed package
        const workspaceRoot = resolve(__dirname, "../../..");
        const consolidatedCss = consolidateCssFiles(sources, addSourceComments, banner, workspaceRoot);

        // Add the consolidated CSS file to the bundle
        this.emitFile({
          type: "asset",
          fileName: outputFileName,
          source: consolidatedCss,
        });

        console.info(
          `✅ CSS Consolidator: Successfully consolidated ${sources.length} CSS files into ${outputFileName}`,
        );
      } catch (error) {
        this.error(`CSS Consolidator failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  };
}

/**
 * Consolidates multiple CSS files into a single string
 */
function consolidateCssFiles(sources: string[], addSourceComments: boolean, banner: string, rootDir?: string): string {
  const cssContents: string[] = [];

  // Add banner if provided
  if (banner) {
    cssContents.push(banner);
    cssContents.push(""); // Empty line after banner
  }

  for (const sourcePath of sources) {
    try {
      // Resolve the path relative to the root directory (workspace root) or current working directory
      const resolvedPath = rootDir ? resolve(rootDir, sourcePath) : resolve(sourcePath);

      if (!existsSync(resolvedPath)) {
        throw new Error(`CSS file not found: ${sourcePath} (resolved to: ${resolvedPath})`);
      }

      const cssContent = readFileSync(resolvedPath, "utf-8");

      if (addSourceComments) {
        cssContents.push(`/* === Source: ${sourcePath} === */`);
      }

      // Add the CSS content, ensuring it ends with a newline
      const trimmedContent = cssContent.trim();
      if (trimmedContent) {
        cssContents.push(trimmedContent);
        cssContents.push(""); // Empty line between files
      }
    } catch (error) {
      throw new Error(
        `Failed to read CSS file "${sourcePath}": ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return cssContents.join("\n");
}

/**
 * Default configuration for CSCS Agent CSS files
 */
export const cscsAgentCssConfig: CssConsolidatorOptions = {
  sources: [
    "packages/core/dist/core-tailwind.css",
    "packages/presets/dist/presets-tailwind.css",
    "packages/icons/dist/icons.css",
  ],
  outputFileName: "cscs-agent-styles.css",
  addSourceComments: true,
  banner:
    "/* CSCS Agent - Consolidated Styles */\n/* This file contains all CSS dependencies for the CSCS Agent embed package */",
};
