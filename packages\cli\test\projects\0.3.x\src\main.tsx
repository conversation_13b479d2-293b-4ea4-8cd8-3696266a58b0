import "@@/@cscs-agent/core/dist/agent-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
import "@@/@cscs-agent/icons/dist/icons.css";

import "./styles.css";

import dayjs from "dayjs";

import { createDefaultRouter, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, Login, defaultAuthGuard } from "@cscs-agent/presets";

import Home from "./pages/home";

dayjs.locale("zh-cn");

const router = createDefaultRouter({
  pages: {
    home: {
      Component: Home,
    },
    chat: {
      Component: Chat,
    },
    agentHome: {
      Component: AgentHome,
    },
    login: {
      enable: true,
      Component: Login,
    },
  },
  authGuard: defaultAuthGuard,
});

initApp({
  loginUrl: "/login",
  router,
}).then(() => {
  console.log("App initialized successfully");
});
