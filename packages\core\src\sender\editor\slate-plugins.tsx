import React, { use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON><PERSON>, Editor, Element as SlateElement, Text, Transforms } from "slate";
import { jsx } from "slate-hyperscript";

import { DatePickerElement, EditableTagElement, RangePickerElement, SelectElement, TagElement } from "./elements";

// Plugin to handle custom elements
export const withCustomElements = (editor: Editor) => {
  const { isInline, isVoid, normalizeNode } = editor;

  // Define which elements are inline
  editor.isInline = (element: SlateElement) => {
    return [
      "embedded-tag",
      "embedded-editable-tag",
      "embedded-select",
      "embedded-datepicker",
      "embedded-rangepicker",
    ].includes(element.type)
      ? true
      : isInline(element);
  };

  // Define which elements are void (cannot contain text)
  editor.isVoid = (element: SlateElement) => {
    return ["embedded-tag", "embedded-select", "embedded-datepicker", "embedded-rangepicker"].includes(element.type)
      ? true
      : isVoid(element);
  };

  // Normalize nodes to ensure proper structure
  editor.normalizeNode = (entry) => {
    const [node, path] = entry;

    // If it's an element node
    if (SlateElement.isElement(node)) {
      // Ensure tag elements have proper structure
      if (node.type === "embedded-tag") {
        // Tag elements should be void and have empty text children
        if (node.children.length !== 1 || node.children[0].text !== "") {
          Transforms.removeNodes(editor, { at: path.concat([0]) });
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure select elements have proper structure
      if (node.type === "embedded-select") {
        // Select elements should be void and have empty text children
        if (node.children.length !== 1 || node.children[0].text !== "") {
          Transforms.removeNodes(editor, { at: path.concat([0]) });
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure editable-tag elements have at least one text child
      if (node.type === "embedded-editable-tag") {
        if (node.children.length === 0) {
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure paragraph elements have at least one text child
      if (node.type === "paragraph") {
        if (node.children.length === 0) {
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }
    }

    // Fall back to the original `normalizeNode` to enforce other constraints
    normalizeNode(entry);
  };

  return editor;
};

/**
 *
 * @description 解析HTML字符串为Slate节点
 * @param el
 * @param markAttributes
 * @returns
 */
export const deserialize = (el: HTMLElement, markAttributes = {}): any => {
  if (el.nodeType === Node.TEXT_NODE) {
    return jsx("text", markAttributes, el.textContent);
  } else if (el.nodeType !== Node.ELEMENT_NODE) {
    return null;
  }

  const nodeAttributes = { ...markAttributes };

  const children = Array.from(el.childNodes)
    .map((node) => deserialize(node as HTMLElement, nodeAttributes))
    .flat();

  if (children.length === 0) {
    children.push(jsx("text", nodeAttributes, ""));
  }

  if (el.nodeType === Node.ELEMENT_NODE) {
    if (el.nodeName === "P") {
      return jsx("element", { type: "paragraph" }, children);
    }
    if (el.nodeName === "EMBEDDED-TAG") {
      return jsx(
        "element",
        {
          type: "embedded-tag",
          content: el.getAttribute("content") || "",
          rawValue: el.getAttribute("rawValue") || "",
          tooltips: el.getAttribute("tooltips") || "",
        },
        [{ text: el.textContent || "" }],
      );
    }
    if (el.nodeName === "EMBEDDED-EDITABLE-TAG") {
      return jsx(
        "element",
        {
          type: "embedded-editable-tag",
          placeholder: el.getAttribute("placeholder") || "",
        },
        [{ text: el.textContent || "" }],
      );
    }
    if (el.nodeName === "EMBEDDED-SELECT") {
      const attrs = el.getAttribute("options") || "[]";
      const options = JSON.parse(attrs.replace(/&quot;/g, '"'));

      return jsx(
        "element",
        {
          type: "embedded-select",
          placeholder: el.getAttribute("placeholder") || "",
          options,
          defaultValue: el.getAttribute("defaultValue") || "",
          tooltips: el.getAttribute("tooltips") || "",
          disabled: el.getAttribute("disabled") === "true",
          apiConfig: el.getAttribute("apiConfig") ? JSON.parse(el.getAttribute("apiConfig") || "{}") : undefined,
          style: el.getAttribute("style") ? JSON.parse(el.getAttribute("style") || "{}") : undefined,
          mode: el.getAttribute("mode"),
        },
        [{ text: "" }],
      );
    }
    if (el.nodeName === "EMBEDDED-DATEPICKER") {
      return jsx(
        "element",
        {
          type: "embedded-datepicker",
          placeholder: el.getAttribute("placeholder") || "",
          defaultValue: el.getAttribute("defaultValue") || "",
          tooltips: el.getAttribute("tooltips") || "",
          disabled: el.getAttribute("disabled") === "true",
          format: el.getAttribute("format") || "YYYY-MM-DD",
          showTime: el.getAttribute("showTime") === "true",
          allowClear: el.getAttribute("allowClear") !== "false",
        },
        [{ text: "" }],
      );
    }
    if (el.nodeName === "EMBEDDED-RANGEPICKER") {
      const placeholderStr = el.getAttribute("placeholder") || "开始日期,结束日期";
      const placeholder = placeholderStr.split(",") as [string, string];
      const defaultValueStr = el.getAttribute("defaultValue") || ",";
      const defaultValue = defaultValueStr.split(",") as [string, string];

      return jsx(
        "element",
        {
          type: "embedded-rangepicker",
          placeholder,
          defaultValue,
          tooltips: el.getAttribute("tooltips") || "",
          disabled: el.getAttribute("disabled") === "true",
          format: el.getAttribute("format") || "YYYY-MM-DD",
          showTime: el.getAttribute("showTime") === "true",
          allowClear: el.getAttribute("allowClear") !== "false",
          separator: el.getAttribute("separator") || "~",
        },
        [{ text: "" }],
      );
    }
  }

  return children;
};

/**
 * @description 序列化Slate节点为HTML字符串
 * @returns
 */
export const useSerializeToHTML = () => {
  const serializeToHTML = useCallback((nodes: Descendant[]): string => {
    return nodes
      .map((node) => {
        if (SlateElement.isElement(node)) {
          switch (node.type) {
            case "embedded-tag": {
              return `<embedded-tag text="${node.content || ""}" rawValue="${node.rawValue || ""}" tooltips="${node.tooltips || ""}"></embedded-tag>`;
            }
            case "embedded-editable-tag": {
              const content = node.children ? serializeToHTML(node.children) : "";
              return `<embedded-editable-tag placeholder="${node.placeholder || ""}">${content}</embedded-editable-tag>`;
            }
            case "embedded-select": {
              const optionsStr = JSON.stringify(node.options || []).replace(/"/g, "&quot;");
              const apiConfigStr = node.apiConfig ? JSON.stringify(node.apiConfig).replace(/"/g, "&quot;") : "";
              return `<embedded-select placeholder="${node.placeholder || "Please select..."}" options="${optionsStr}" ${apiConfigStr ? `apiConfig="${apiConfigStr}"` : ""} defaultValue="${node.defaultValue || ""}" tooltips="${node.tooltips || ""}" disabled="${node.disabled || false}"></embedded-select>`;
            }
            case "embedded-datepicker": {
              return `<embedded-datepicker placeholder="${node.placeholder || ""}" defaultValue="${node.defaultValue || ""}" tooltips="${node.tooltips || ""}" disabled="${node.disabled || false}" format="${node.format || "YYYY-MM-DD"}" showTime="${node.showTime || false}" allowClear="${node.allowClear !== false}"></embedded-datepicker>`;
            }
            case "embedded-rangepicker": {
              const placeholderStr = Array.isArray(node.placeholder) ? node.placeholder.join(",") : "开始日期,结束日期";
              const defaultValueStr = Array.isArray(node.defaultValue) ? node.defaultValue.join(",") : ",";
              return `<embedded-rangepicker placeholder="${placeholderStr}" defaultValue="${defaultValueStr}" tooltips="${node.tooltips || ""}" disabled="${node.disabled || false}" format="${node.format || "YYYY-MM-DD"}" showTime="${node.showTime || false}" allowClear="${node.allowClear !== false}" separator="${node.separator || "~"}"></embedded-rangepicker>`;
            }
            case "paragraph": {
              const content = node.children ? serializeToHTML(node.children) : "";
              return content ? `<p>${content}</p>` : "";
            }
          }
        }
        if (Text.isText(node)) {
          return node.text;
        }
        return "";
      })
      .join("");
  }, []);

  return serializeToHTML;
};

/**
 * @description 序列化Slate节点为纯文本
 * @returns
 */
export const useSerializeToText = () => {
  // Convert Slate value to plain text
  const serializeToText = useCallback((document: Descendant[]): string => {
    const serialize = (nodes: Descendant[]) => {
      const newNodes: string[] = nodes.map((node) => {
        if (SlateElement.isElement(node)) {
          switch (node.type) {
            case "paragraph":
              return (Array.isArray(node.children) ? serialize(node.children) : "") + "\n";
            case "embedded-tag":
              return node.rawValue || node.text || "";
            case "embedded-editable-tag":
              return node.children.map((child: Descendant) => (Text.isText(child) ? child.text : "")).join("");
            case "embedded-select":
              return node.defaultValue || "";
            case "embedded-datepicker":
              return node.defaultValue || "";
            case "embedded-rangepicker":
              return Array.isArray(node.defaultValue) ? node.defaultValue.join(" ~ ") : "";
            default:
              return Array.isArray(node.children) ? serialize(node.children) : "";
          }
        }
        if (Text.isText(node)) {
          return node.text;
        }
        return "";
      });

      return newNodes.join("");
    };

    return serialize(document);
  }, []);

  return serializeToText;
};

// Render custom elements
export const useRenderElement = () => {
  // Render custom elements
  const renderElement = useCallback((props: any) => {
    switch (props.element.type) {
      case "embedded-tag":
        return <TagElement {...props} />;
      case "embedded-editable-tag":
        return <EditableTagElement {...props} />;
      case "embedded-select":
        return <SelectElement {...props} />;
      case "embedded-datepicker":
        return <DatePickerElement {...props} />;
      case "embedded-rangepicker":
        return <RangePickerElement {...props} />;
      case "paragraph":
      default:
        return <p {...props.attributes}>{props.children}</p>;
    }
  }, []);

  return renderElement;
};
